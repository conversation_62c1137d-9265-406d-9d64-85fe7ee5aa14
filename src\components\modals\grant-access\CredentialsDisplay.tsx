
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';
import { Copy, Download, Mail } from 'lucide-react';
import { SuccessCredentials } from './types';
import { copyToClipboard, generateCredentialsPDF } from '@/utils/resident-access-helpers';
import { toast } from 'sonner';

interface CredentialsDisplayProps {
  credentials: SuccessCredentials;
  sendEmailToResident: boolean;
  onClose: () => void;
}

export const CredentialsDisplay: React.FC<CredentialsDisplayProps> = ({
  credentials,
  sendEmailToResident,
  onClose
}) => {
  const handleCopyCredentials = async () => {
    const credentialsText = `Email: ${credentials.email}\nSenha: ${credentials.password}`;
    
    const success = await copyToClipboard(credentialsText);
    if (success) {
      toast.success('Credenciais copiadas!');
    } else {
      toast.error('Erro ao copiar credenciais');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      toast.loading('Gerando PDF...', { id: 'pdf-generation' });
      await generateCredentialsPDF(credentials);
      toast.dismiss('pdf-generation');
      toast.success('PDF gerado com sucesso!');
    } catch (error) {
      toast.dismiss('pdf-generation');
      console.error('Erro ao gerar PDF:', error);
      toast.error('Erro ao gerar PDF das credenciais');
    }
  };

  return (
    <div className="space-y-4">
      <div className="p-4 bg-green-50 rounded-lg">
        <h4 className="font-medium text-green-900 mb-2">✅ Acesso Criado com Sucesso!</h4>
        <div className="space-y-2 text-sm text-green-700">
          <p><strong>Email:</strong> {credentials.email}</p>
          <p><strong>Senha:</strong> {credentials.password}</p>
          <p><strong>Apartamento:</strong> {credentials.apartment}</p>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="font-medium">Compartilhar Credenciais</h4>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <Button
            variant="outline"
            onClick={handleCopyCredentials}
            className="w-full"
          >
            <Copy className="h-4 w-4 mr-2" />
            Copiar
          </Button>
          
          <Button
            variant="outline"
            onClick={handleDownloadPDF}
            className="w-full"
          >
            <Download className="h-4 w-4 mr-2" />
            Baixar PDF
          </Button>
        </div>

        {sendEmailToResident && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700">
              <Mail className="h-4 w-4" />
              <span className="text-sm font-medium">Email Enviado</span>
            </div>
            <div className="text-sm text-blue-600 mt-1">
              <p>• Para o morador: {credentials.email}</p>
            </div>
          </div>
        )}
      </div>

      <DialogFooter>
        <Button onClick={onClose}>
          Fechar
        </Button>
      </DialogFooter>
    </div>
  );
};
