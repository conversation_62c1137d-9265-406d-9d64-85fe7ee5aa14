/**
 * Tests for Sequential Payment Logic
 * 
 * This file tests the sequential payment validation to ensure users
 * must pay quotas and fines in chronological order.
 */

import { validateSequentialPayment, validateSequentialFineRegularization } from '../quota-calculations';

// Mock quota data for testing
const createMockQuota = (id: string, moradorId: string, mes: number, ano: number, status: string, multa: number = 0) => ({
  id,
  morador_id: moradorId,
  mes,
  ano,
  status,
  multa,
  isento_quotas: false
});

describe('Sequential Payment Validation', () => {
  describe('validateSequentialPayment', () => {
    it('should allow payment when no previous unpaid quotas exist', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Pago'),
        createMockQuota('2', 'morador1', 2, 2025, 'Pago'),
        createMockQuota('3', 'morador1', 3, 2025, 'Não Pago'), // This one to pay
      ];

      const result = validateSequentialPayment('3', quotas);
      expect(result.isValid).toBe(true);
    });

    it('should block payment when previous months are unpaid', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Não Pago'), // Unpaid January
        createMockQuota('2', 'morador1', 2, 2025, 'Não Pago'), // Unpaid February
        createMockQuota('3', 'morador1', 3, 2025, 'Não Pago'), // Trying to pay March
      ];

      const result = validateSequentialPayment('3', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Janeiro/2025, Fevereiro/2025');
      expect(result.unpaidQuotas).toHaveLength(2);
    });

    it('should block payment when only one previous month is unpaid', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Pago'),
        createMockQuota('2', 'morador1', 2, 2025, 'Não Pago'), // Unpaid February
        createMockQuota('3', 'morador1', 3, 2025, 'Não Pago'), // Trying to pay March
      ];

      const result = validateSequentialPayment('3', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Fevereiro/2025');
      expect(result.unpaidQuotas).toHaveLength(1);
    });

    it('should handle cross-year scenarios correctly', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 11, 2024, 'Não Pago'), // Unpaid November 2024
        createMockQuota('2', 'morador1', 12, 2024, 'Pago'),     // Paid December 2024
        createMockQuota('3', 'morador1', 1, 2025, 'Não Pago'),  // Trying to pay January 2025
      ];

      const result = validateSequentialPayment('3', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Novembro/2024');
    });

    it('should only consider quotas from the same resident', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Não Pago'), // Different resident
        createMockQuota('2', 'morador2', 2, 2025, 'Não Pago'), // This resident, trying to pay
      ];

      const result = validateSequentialPayment('2', quotas);
      expect(result.isValid).toBe(true); // Should be valid because unpaid quota is from different resident
    });

    it('should return error for already paid quota', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Pago'), // Already paid
      ];

      const result = validateSequentialPayment('1', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('já foi paga');
    });

    it('should return error for non-existent quota', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Não Pago'),
      ];

      const result = validateSequentialPayment('999', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('não encontrada');
    });

    it('should handle complex scenario with multiple residents and mixed payments', () => {
      const quotas = [
        // Resident 1
        createMockQuota('1', 'morador1', 1, 2025, 'Pago'),
        createMockQuota('2', 'morador1', 2, 2025, 'Não Pago'),
        createMockQuota('3', 'morador1', 3, 2025, 'Não Pago'),
        // Resident 2
        createMockQuota('4', 'morador2', 1, 2025, 'Pago'),
        createMockQuota('5', 'morador2', 2, 2025, 'Pago'),
        createMockQuota('6', 'morador2', 3, 2025, 'Não Pago'), // This one should be allowed
      ];

      // Resident 1 trying to pay March (should be blocked due to unpaid February)
      const result1 = validateSequentialPayment('3', quotas);
      expect(result1.isValid).toBe(false);

      // Resident 2 trying to pay March (should be allowed)
      const result2 = validateSequentialPayment('6', quotas);
      expect(result2.isValid).toBe(true);
    });
  });

  describe('validateSequentialFineRegularization', () => {
    it('should allow fine regularization when no previous unpaid fines exist', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Pago', 0),   // No fine
        createMockQuota('2', 'morador1', 2, 2025, 'Pago', 0),   // No fine
        createMockQuota('3', 'morador1', 3, 2025, 'Pago', 500), // Has fine to regularize
      ];

      const result = validateSequentialFineRegularization('3', quotas);
      expect(result.isValid).toBe(true);
    });

    it('should block fine regularization when previous fines are unpaid', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Não Pago', 500), // Unpaid fine January
        createMockQuota('2', 'morador1', 2, 2025, 'Não Pago', 500), // Unpaid fine February
        createMockQuota('3', 'morador1', 3, 2025, 'Não Pago', 500), // Trying to regularize March
      ];

      const result = validateSequentialFineRegularization('3', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Janeiro/2025, Fevereiro/2025');
      expect(result.unpaidFines).toHaveLength(2);
    });

    it('should return error for quota without fine', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Pago', 0), // No fine
      ];

      const result = validateSequentialFineRegularization('1', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('não possui multa');
    });

    it('should only consider quotas with fines from the same resident', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Não Pago', 500), // Different resident with fine
        createMockQuota('2', 'morador2', 2, 2025, 'Não Pago', 500), // This resident, trying to regularize
      ];

      const result = validateSequentialFineRegularization('2', quotas);
      expect(result.isValid).toBe(true); // Should be valid because unpaid fine is from different resident
    });

    it('should handle cross-year fine scenarios correctly', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 11, 2024, 'Não Pago', 500), // Unpaid fine November 2024
        createMockQuota('2', 'morador1', 12, 2024, 'Pago', 0),       // No fine December 2024
        createMockQuota('3', 'morador1', 1, 2025, 'Não Pago', 500),  // Trying to regularize January 2025
      ];

      const result = validateSequentialFineRegularization('3', quotas);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('Novembro/2024');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty quota list', () => {
      const result = validateSequentialPayment('1', []);
      expect(result.isValid).toBe(false);
      expect(result.message).toContain('não encontrada');
    });

    it('should handle single quota payment', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Não Pago'),
      ];

      const result = validateSequentialPayment('1', quotas);
      expect(result.isValid).toBe(true); // No previous quotas to check
    });

    it('should handle quotas with same month/year but different residents', () => {
      const quotas = [
        createMockQuota('1', 'morador1', 1, 2025, 'Não Pago'),
        createMockQuota('2', 'morador2', 1, 2025, 'Não Pago'),
      ];

      // Both should be valid since they're different residents
      expect(validateSequentialPayment('1', quotas).isValid).toBe(true);
      expect(validateSequentialPayment('2', quotas).isValid).toBe(true);
    });
  });
});
