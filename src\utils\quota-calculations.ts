
import { format, differenceInDays, parseISO, isAfter, isBefore, isEqual } from 'date-fns';

export interface MultaConfig {
  valorMulta: number;
  diasAtrasoMulta: number;
}

export const calculateFineAmount = (
  dataPagamento: string | null,
  dataVencimento: string,
  config: MultaConfig
): number => {
  console.log('🎯 Calculando multa:', {
    dataPagamento,
    dataVencimento,
    config
  });

  const vencimento = parseISO(dataVencimento);
  const hoje = new Date();
  
  // Se a quota foi paga
  if (dataPagamento) {
    const pagamento = parseISO(dataPagamento);
    const diffDays = differenceInDays(pagamento, vencimento);
    
    console.log('📅 Diferença em dias (pagamento vs vencimento):', diffDays);
    console.log('📋 Dias tolerância configurados:', config.diasAtrasoMulta);
    
    // Se pagou APÓS o vencimento + tolerância
    if (diffDays > config.diasAtrasoMulta) {
      console.log('💰 Aplicando multa por pagamento em atraso:', config.valorMulta, 'Kz');
      return config.valorMulta;
    }
    
    console.log('✅ Sem multa - pagamento dentro do prazo');
    return 0;
  }
  
  // Se a quota NÃO foi paga, verificar se já está em atraso
  const diffDaysFromToday = differenceInDays(hoje, vencimento);
  
  console.log('📅 Diferença em dias (hoje vs vencimento):', diffDaysFromToday);
  
  // Se já passou do vencimento + tolerância
  if (diffDaysFromToday > config.diasAtrasoMulta) {
    console.log('💰 Aplicando multa por atraso:', config.valorMulta, 'Kz');
    return config.valorMulta;
  }
  
  console.log('✅ Sem multa - ainda dentro do prazo');
  return 0;
};

export const calculateQuotaStatus = (
  dataPagamento: string | null,
  dataVencimento: string
): string => {
  if (!dataPagamento) {
    const hoje = new Date();
    const vencimento = parseISO(dataVencimento);
    return isAfter(hoje, vencimento) ? 'Não Pago' : 'Não Pago';
  }
  return 'Pago';
};

export const calculateSituacaoMulta = (
  multa: number,
  dataPagamento: string | null,
  dataVencimento: string
): string => {
  console.log('🎯 Calculando situação da multa:', { multa, dataPagamento, dataVencimento });
  
  // Se não há multa, está regularizada
  if (multa === 0) {
    console.log('✅ Regularizada - sem multa');
    return 'Regularizada';
  }
  
  // Se há multa mas a quota foi paga, a situação da multa depende de se a multa foi regularizada
  // Para este cálculo automático, assumimos que se há multa > 0, não está regularizada
  if (multa > 0) {
    console.log('❌ Não Regularizada - há multa pendente');
    return 'Não Regularizada';
  }
  
  return 'Regularizada';
};

// Validação inteligente de multa baseada na data atual
export const shouldApplyFine = (
  dataVencimento: string,
  config: MultaConfig,
  dataPagamento?: string | null
): boolean => {
  const vencimento = parseISO(dataVencimento);
  const hoje = new Date();
  
  // Se tem data de pagamento, verificar se pagou em atraso
  if (dataPagamento) {
    const pagamento = parseISO(dataPagamento);
    const diffDays = differenceInDays(pagamento, vencimento);
    return diffDays > config.diasAtrasoMulta;
  }
  
  // Se não foi pago, verificar se já passou do prazo
  const diffDaysFromToday = differenceInDays(hoje, vencimento);
  return diffDaysFromToday > config.diasAtrasoMulta;
};

// Correção automática de multa ao marcar como pago
export const correctFineOnPayment = (
  dataPagamento: string,
  dataVencimento: string,
  config: MultaConfig
): { shouldHaveFine: boolean; newFineAmount: number } => {
  const pagamento = parseISO(dataPagamento);
  const vencimento = parseISO(dataVencimento);
  
  const diffDays = differenceInDays(pagamento, vencimento);
  const shouldHaveFine = diffDays > config.diasAtrasoMulta;
  const newFineAmount = shouldHaveFine ? config.valorMulta : 0;
  
  console.log('🔧 Correção automática de multa:', {
    dataPagamento,
    dataVencimento,
    diffDays,
    shouldHaveFine,
    newFineAmount
  });
  
  return { shouldHaveFine, newFineAmount };
};



// Nova função para validar se uma quota pode ser criada
export const validateQuotaCreation = (
  mes: number,
  ano: number,
  dataVencimento?: string
): { isValid: boolean; message?: string } => {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const currentYear = currentDate.getFullYear();
  
  // Validar se o mês/ano não é muito futuro
  if (ano > currentYear + 1 || (ano === currentYear + 1 && mes > currentMonth)) {
    return {
      isValid: false,
      message: 'Não é possível criar quotas muito distantes no futuro'
    };
  }
  
  // Validar se o mês/ano não é muito antigo
  if (ano < 2020) {
    return {
      isValid: false,
      message: 'Ano muito antigo para criação de quotas'
    };
  }
  
  return { isValid: true };
};
