
/**
 * App.tsx - Componente Principal da Aplicação
 *
 * Arquivo raiz que configura todos os providers, roteamento e estrutura base
 * do sistema de gestão de condomínio Prédio Azul.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

// Componentes de UI e notificações
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";

// React Query para gerenciamento de estado
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// Roteamento
import { BrowserRouter, Routes, Route } from "react-router-dom";

// Contextos da aplicação
import { AuthProvider } from "@/contexts/AuthContext";
import { SidebarProvider } from "@/contexts/SidebarContext";

// Componentes de autenticação e proteção de rotas
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import RoleBasedRoute from "@/components/auth/RoleBasedRoute";

// Hooks e utilitários
import { useEffect } from "react";
import { ensureAvatarsBucket } from "@/utils/supabase-helpers";

// Páginas Administrativas
import AdminDashboard from "./pages/admin/Dashboard";
import Residents from "./pages/Residents";
import CommitteeMembers from "./pages/CommitteeMembers";
import Quotas from "./pages/Quotas";
import CashFlow from "./pages/CashFlow";
import Users from "./pages/Users";
import Documents from "./pages/Documents";
import Reports from "./pages/Reports";
import Settings from "./pages/Settings";

// Páginas dos Moradores
import ResidentDashboard from "./pages/resident/Dashboard";
import ResidentQuotas from "./pages/resident/Quotas";
import ResidentProfile from "./pages/resident/Profile";
import ResidentDocuments from "./pages/resident/Documents";
import ResidentCommunications from "./pages/resident/Communications";

// Páginas de Autenticação
import Login from "./pages/auth/Login";

// Páginas Comuns
import NotFound from "./pages/NotFound";

// Configuração do cliente React Query
const queryClient = new QueryClient();

/**
 * Componente principal da aplicação
 *
 * Configura todos os providers necessários em ordem hierárquica:
 * 1. QueryClient - Gerenciamento de estado e cache
 * 2. AuthProvider - Contexto de autenticação
 * 3. SidebarProvider - Contexto da sidebar
 * 4. TooltipProvider - Tooltips globais
 * 5. BrowserRouter - Roteamento
 *
 * @returns {JSX.Element} Aplicação completa com providers
 */
const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <SidebarProvider>
          <TooltipProvider>
            {/* Componentes de notificação globais */}
            <Toaster />
            <Sonner />

            {/* Roteador principal */}
            <BrowserRouter>
              <AppRoutes />
            </BrowserRouter>
          </TooltipProvider>
        </SidebarProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

/**
 * Componente de roteamento da aplicação
 *
 * Separado do App principal para permitir uso de hooks dentro do contexto.
 * Configura todas as rotas protegidas e baseadas em roles.
 *
 * @returns {JSX.Element} Sistema de rotas completo
 */
const AppRoutes = () => {
  // Garante que o bucket de avatares existe na inicialização
  useEffect(() => {
    ensureAvatarsBucket();
  }, []);

  return (
    <Routes>
      {/* Auth Routes */}
      <Route path="/login" element={<Login />} />

      {/* Admin Routes */}
      <Route path="/" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <AdminDashboard />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/admin/dashboard" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <AdminDashboard />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/residents" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <Residents />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/committee-members" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <CommitteeMembers />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/quotas" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <Quotas />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/cash-flow" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <CashFlow />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/users" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <Users />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/documents" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <Documents />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/reports" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <Reports />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/settings" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['admin']}>
            <Settings />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />

      {/* Resident Routes */}
      <Route path="/resident/dashboard" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['resident']}>
            <ResidentDashboard />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/resident/quotas" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['resident']}>
            <ResidentQuotas />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/resident/profile" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['resident']}>
            <ResidentProfile />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/resident/documents" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['resident']}>
            <ResidentDocuments />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />
      <Route path="/resident/communications" element={
        <ProtectedRoute>
          <RoleBasedRoute allowedRoles={['resident']}>
            <ResidentCommunications />
          </RoleBasedRoute>
        </ProtectedRoute>
      } />

      {/* 404 Route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default App;
