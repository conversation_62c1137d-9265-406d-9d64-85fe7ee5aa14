-- Migration para limpeza de usuários órfãos
-- Data: 22/06/2025
-- Objetivo: Resolver problema de usuários que existem em auth.users mas não em profiles

-- Função administrativa para limpar usuários órfãos (sem verificação de auth.uid())
CREATE OR REPLACE FUNCTION public.admin_cleanup_orphaned_users()
RETURNS TABLE(
  action TEXT,
  email TEXT,
  user_id UUID,
  success BOOLEAN,
  message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  orphaned_user RECORD;
  cleanup_count INTEGER := 0;
BEGIN
  -- Encontrar usuários que existem em auth.users mas não em profiles
  FOR orphaned_user IN 
    SELECT au.id, au.email, au.created_at
    FROM auth.users au
    LEFT JOIN public.profiles p ON au.id = p.id
    WHERE p.id IS NULL
      AND au.email IS NOT NULL
  LOOP
    BEGIN
      -- Tentar eliminar o usuário órfão de auth.users
      -- Nota: Esta operação requer privilégios de service_role
      PERFORM auth.admin_delete_user(orphaned_user.id);
      
      cleanup_count := cleanup_count + 1;
      
      RETURN QUERY SELECT 
        'DELETE_AUTH_USER'::TEXT,
        orphaned_user.email,
        orphaned_user.id,
        true,
        'Usuário órfão eliminado com sucesso'::TEXT;
        
    EXCEPTION WHEN OTHERS THEN
      RETURN QUERY SELECT 
        'DELETE_AUTH_USER'::TEXT,
        orphaned_user.email,
        orphaned_user.id,
        false,
        SQLERRM::TEXT;
    END;
  END LOOP;
  
  -- Retornar resumo
  RETURN QUERY SELECT 
    'SUMMARY'::TEXT,
    NULL::TEXT,
    NULL::UUID,
    true,
    format('Total de usuários órfãos processados: %s', cleanup_count)::TEXT;
END;
$$;

-- Função para listar usuários órfãos (apenas para diagnóstico)
CREATE OR REPLACE FUNCTION public.list_orphaned_users()
RETURNS TABLE(
  user_id UUID,
  email TEXT,
  created_at TIMESTAMPTZ,
  status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email,
    au.created_at,
    'ORPHANED_IN_AUTH'::TEXT as status
  FROM auth.users au
  LEFT JOIN public.profiles p ON au.id = p.id
  WHERE p.id IS NULL
    AND au.email IS NOT NULL
  ORDER BY au.created_at DESC;
END;
$$;

-- Função melhorada para eliminar usuário completamente (com melhor tratamento de erros)
CREATE OR REPLACE FUNCTION public.delete_user_completely_v2(p_email text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_record RECORD;
  auth_user_record RECORD;
  result jsonb;
  deleted_profile BOOLEAN := false;
  deleted_access BOOLEAN := false;
  deleted_auth BOOLEAN := false;
BEGIN
  -- Verificar se o utilizador atual é admin (apenas quando chamado via aplicação)
  IF auth.uid() IS NOT NULL THEN
    IF NOT EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    ) THEN
      RETURN jsonb_build_object(
        'success', false,
        'error', 'Acesso negado: privilégios de administrador necessários'
      );
    END IF;
  END IF;

  -- Encontrar o utilizador pelo email na tabela profiles
  SELECT p.id, p.email, p.name 
  INTO user_record
  FROM public.profiles p 
  WHERE p.email = p_email;

  -- Se encontrado em profiles, eliminar registros relacionados
  IF FOUND THEN
    -- Eliminar acessos de apartamento
    DELETE FROM public.apartment_access 
    WHERE user_id = user_record.id;
    deleted_access := true;

    -- Eliminar perfil
    DELETE FROM public.profiles 
    WHERE id = user_record.id;
    deleted_profile := true;
  END IF;

  -- Procurar também em auth.users (pode existir órfão)
  SELECT au.id, au.email
  INTO auth_user_record
  FROM auth.users au
  WHERE au.email = p_email;

  IF FOUND THEN
    BEGIN
      -- Tentar eliminar de auth.users
      PERFORM auth.admin_delete_user(auth_user_record.id);
      deleted_auth := true;
    EXCEPTION WHEN OTHERS THEN
      -- Se falhar, registrar o erro mas continuar
      deleted_auth := false;
    END;
  END IF;

  -- Construir resultado
  result := jsonb_build_object(
    'success', true,
    'email', p_email,
    'deletions', jsonb_build_object(
      'profile', deleted_profile,
      'access', deleted_access,
      'auth', deleted_auth
    )
  );

  -- Se nenhuma eliminação foi feita
  IF NOT (deleted_profile OR deleted_access OR deleted_auth) THEN
    result := jsonb_build_object(
      'success', false,
      'error', 'Usuário não encontrado em nenhuma tabela',
      'email', p_email
    );
  END IF;

  RETURN result;
END;
$$;

-- Comentários sobre o uso:
-- 1. Para listar usuários órfãos: SELECT * FROM public.list_orphaned_users();
-- 2. Para limpar usuários órfãos: SELECT * FROM public.admin_cleanup_orphaned_users();
-- 3. Para eliminar usuário específico: SELECT public.delete_user_completely_v2('<EMAIL>');
