import React, { useState, useEffect } from 'react';
import Sidebar from '@/components/layout/Sidebar';
import Header from '@/components/layout/Header';
import DataTable from '@/components/tables/DataTable';
import EditResidentModal from '@/components/modals/EditResidentModal';
import { GrantAccessModal } from '@/components/modals/GrantAccessModal';
import { ExemptionManager } from '@/components/resident/ExemptionManager';
import { AccessStatusBadge } from '@/components/resident/AccessStatusBadge';

import { useSidebar } from '@/contexts/SidebarContext';
import { useResidentAccess } from '@/hooks/useResidentAccess';
import { cn } from '@/lib/utils';
import { UserPlus, Mail, Phone, Shield, Key, UserX } from 'lucide-react';
import { toast } from 'sonner';
import { Morador } from '@/types';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getMoradores, createMorador, updateMorador, deleteMorador } from '@/utils/supabase-helpers';
import { z } from "zod";
import { useSearchParams } from 'react-router-dom';
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const newResidentSchema = z.object({
  nome: z.string().min(1, "Nome é obrigatório"),
  apartamento: z.string().min(1, "Apartamento é obrigatório"),
  email: z.string().email("Email inválido").optional().or(z.literal('')),
  telefone: z.string().optional().or(z.literal('')),
  isento_quotas: z.boolean().optional(),
  motivo_isencao: z.string().optional()
}).refine((data) => {
  if (data.isento_quotas && !data.motivo_isencao?.trim()) {
    return false;
  }
  return true;
}, {
  message: "Motivo da isenção é obrigatório quando morador está isento",
  path: ["motivo_isencao"]
});

type NewResidentFormValues = z.infer<typeof newResidentSchema>;

const Residents = () => {
  const { collapsed, isMobile } = useSidebar();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isGrantAccessModalOpen, setIsGrantAccessModalOpen] = useState(false);
  const [selectedResident, setSelectedResident] = useState<Morador | null>(null);
  const [residentToDelete, setResidentToDelete] = useState<Morador | null>(null);
  const [residentToRevokeAccess, setResidentToRevokeAccess] = useState<{ userId: string; apartmentId: string; name: string } | null>(null);
  const [residentToDeleteCompletely, setResidentToDeleteCompletely] = useState<{ email: string; name: string } | null>(null);
  const [residentToRevokeAndDelete, setResidentToRevokeAndDelete] = useState<{ email: string; name: string } | null>(null);
  const [showExemptOnly, setShowExemptOnly] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    const showExempt = searchParams.get('showExempt');
    if (showExempt === 'true') {
      setShowExemptOnly(true);
      setSearchParams({});
    }
  }, [searchParams, setSearchParams]);

  const newResidentForm = useForm<NewResidentFormValues>({
    resolver: zodResolver(newResidentSchema),
    defaultValues: {
      nome: '',
      apartamento: '',
      email: '',
      telefone: '',
      isento_quotas: false,
      motivo_isencao: ''
    }
  });

  const { data: moradores = [], isLoading } = useQuery({
    queryKey: ['moradores'],
    queryFn: getMoradores
  });

  const createResidentMutation = useMutation({
    mutationFn: (data: NewResidentFormValues) => {
      return createMorador({
        nome: data.nome,
        apartamento: data.apartamento,
        email: data.email,
        telefone: data.telefone,
        isento_quotas: data.isento_quotas || false,
        motivo_isencao: data.isento_quotas ? data.motivo_isencao || '' : undefined
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Morador adicionado com sucesso!');
      setIsModalOpen(false);
      newResidentForm.reset();
    },
    onError: (error) => {
      console.error('Error creating resident:', error);
      toast.error('Erro ao adicionar morador.');
    }
  });

  const updateResidentMutation = useMutation({
    mutationFn: (data: { id: string, morador: Partial<Morador> }) => {
      return updateMorador(data.id, data.morador);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Morador atualizado com sucesso!');
      setIsEditModalOpen(false);
    },
    onError: (error) => {
      console.error('Error updating resident:', error);
      toast.error('Erro ao atualizar morador.');
    }
  });

  const deleteResidentMutation = useMutation({
    mutationFn: (id: string) => {
      return deleteMorador(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Morador removido com sucesso!');
      setResidentToDelete(null);
    },
    onError: (error) => {
      console.error('Error deleting resident:', error);
      toast.error('Erro ao remover morador.');
    }
  });

  const {
    residentsWithAccess,
    createUser,
    isCreatingUser,
    createUserError,
    createUserStatus,
    revokeAccess,
    isRevokingAccess,
    deleteUserCompletely,
    isDeletingUser,
    revokeAndDeleteCompletely,
    isRevokingAndDeleting
  } = useResidentAccess();

  const handleCreateResident = (data: NewResidentFormValues) => {
    createResidentMutation.mutate(data);
  };

  const handleEdit = (resident: Morador) => {
    setSelectedResident(resident);
    setIsEditModalOpen(true);
  };

  const handleDelete = (resident: Morador) => {
    setResidentToDelete(resident);
  };

  const confirmDelete = () => {
    if (residentToDelete?.id) {
      deleteResidentMutation.mutate(residentToDelete.id);
    }
  };

  const handleUpdateResident = (residentData: Partial<Morador>) => {
    if (selectedResident?.id) {
      updateResidentMutation.mutate({
        id: selectedResident.id,
        morador: residentData
      });
    }
  };

  const handleGrantAccess = (resident: Morador) => {
    console.log('🎯 [Residents] handleGrantAccess called for:', resident.nome);
    setSelectedResident(resident);
    setIsGrantAccessModalOpen(true);
  };

  const handleCreateUserAccess = (params: { email: string; password: string; name: string; apartment_id: string }) => {
    console.log('🎯 [Residents] handleCreateUserAccess called with params:', params);
    console.log('🔍 [Residents] About to call createUser from useResidentAccess hook');
    
    try {
      createUser(params);
      console.log('🔍 [Residents] createUser called successfully, waiting for mutation...');
    } catch (error) {
      console.error('❌ [Residents] Error calling createUser:', error);
    }
  };

  const handleCloseGrantAccessModal = () => {
    console.log('🚪 [Residents] Closing GrantAccessModal');
    setIsGrantAccessModalOpen(false);
    setTimeout(() => {
      setSelectedResident(null);
    }, 100);
  };

  const handleRevokeAccess = (userId: string, apartmentId: string, name: string) => {
    setResidentToRevokeAccess({ userId, apartmentId, name });
  };

  const confirmRevokeAccess = () => {
    if (residentToRevokeAccess) {
      revokeAccess({
        userId: residentToRevokeAccess.userId,
        apartmentId: residentToRevokeAccess.apartmentId
      });
      setResidentToRevokeAccess(null);
    }
  };

  const handleDeleteUserCompletely = (email: string, name: string) => {
    setResidentToDeleteCompletely({ email, name });
  };

  const confirmDeleteUserCompletely = () => {
    if (residentToDeleteCompletely) {
      deleteUserCompletely(residentToDeleteCompletely.email);
      setResidentToDeleteCompletely(null);
    }
  };

  const handleRevokeAndDeleteCompletely = (email: string, name: string) => {
    setResidentToRevokeAndDelete({ email, name });
  };

  const confirmRevokeAndDeleteCompletely = () => {
    if (residentToRevokeAndDelete) {
      revokeAndDeleteCompletely(residentToRevokeAndDelete.email);
      setResidentToRevokeAndDelete(null);
    }
  };

  const residentsWithAccessMap = new Map(
    residentsWithAccess.map(r => [r.apartment_id, r])
  );

  const enrichedMoradores = moradores.map(morador => {
    const accessInfo = residentsWithAccessMap.get(morador.apartamento);
    return {
      ...morador,
      hasAccess: accessInfo?.has_access || false,
      userId: accessInfo?.user_id || null,
      firstLogin: accessInfo?.first_login || null,
      accessGrantedAt: accessInfo?.access_granted_at || null
    };
  });

  const filteredMoradores = showExemptOnly 
    ? enrichedMoradores.filter(m => m.isento_quotas) 
    : enrichedMoradores;

  const columns = [
    {
      header: 'Nome',
      accessor: 'nome',
      isSortable: true,
      cell: (value: string, row: any) => (
        <div className="flex items-center gap-2">
          <span>{value}</span>
          {row.isento_quotas && (
            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
              <Shield className="w-3 h-3 mr-1" />
              Isento
            </Badge>
          )}
        </div>
      )
    },
    {
      header: 'Apartamento',
      accessor: 'apartamento',
      isSortable: true,
      align: 'center' as const,
    },
    {
      header: 'E-mail',
      accessor: 'email',
      cell: (value: string) => (
        <div className="flex items-center">
          <Mail size={16} className="mr-2 text-gray-500" />
          <span>{value}</span>
        </div>
      )
    },
    {
      header: 'Telefone',
      accessor: 'telefone',
      cell: (value: string) => (
        <div className="flex items-center">
          <Phone size={16} className="mr-2 text-gray-500" />
          <span>{value || '-'}</span>
        </div>
      )
    },
    {
      header: 'Status de Acesso',
      accessor: 'hasAccess',
      cell: (value: boolean, row: any) => (
        <AccessStatusBadge hasAccess={value} firstLogin={row.firstLogin} />
      )
    },
    {
      header: 'Acesso',
      accessor: 'accessActions',
      cell: (value: any, row: any) => (
        <div className="flex gap-1">
          {!row.hasAccess ? (
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleGrantAccess(row)}
              className="text-green-600 hover:text-green-700"
            >
              <Key className="w-4 h-4 mr-1" />
              Conceder
            </Button>
          ) : (
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleRevokeAccess(row.userId, row.apartamento, row.nome)}
                className="text-orange-600 hover:text-orange-700"
                disabled={isRevokingAccess || isRevokingAndDeleting}
                title="Revogar acesso mas manter usuário (pode causar usuários órfãos)"
              >
                <UserX className="w-4 h-4 mr-1" />
                Revogar
              </Button>
              {row.email && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleRevokeAndDeleteCompletely(row.email, row.nome)}
                    className="text-red-600 hover:text-red-700 border-red-200"
                    disabled={isRevokingAndDeleting || isDeletingUser}
                    title="Revogar acesso e eliminar usuário completamente (recomendado)"
                  >
                    <UserX className="w-4 h-4 mr-1" />
                    Revogar & Eliminar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteUserCompletely(row.email, row.nome)}
                    className="text-red-800 hover:text-red-900 border-red-300"
                    disabled={isDeletingUser || isRevokingAndDeleting}
                    title="Eliminar usuário órfão (para casos especiais)"
                  >
                    🗑️ Limpar
                  </Button>
                </>
              )}
            </div>
          )}
        </div>
      )
    }
  ];

  const filters = [
    {
      name: 'Bloco',
      options: [
        { value: 'A', label: 'Bloco A' },
        { value: 'B', label: 'Bloco B' },
        { value: 'C', label: 'Bloco C' },
      ],
      onFilter: (value: string) => {
        console.log('Filtering by block:', value);
      },
    },
  ];

  const moradoresWithActions = filteredMoradores.map(morador => ({
    ...morador,
    onEdit: () => handleEdit(morador),
    onDelete: () => handleDelete(morador)
  }));

  const isExempt = newResidentForm.watch('isento_quotas');

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />

      <div className={cn(
        "flex-1 flex flex-col transition-all duration-300 overflow-hidden",
        isMobile ? "ml-0" : (collapsed ? "ml-12" : "ml-64")
      )}>
        <div className="flex-1 overflow-y-auto pb-10">
          <div className="page-container">
            <Header
              title="Moradores"
              subtitle="Gerencie a lista de moradores e seus acessos ao sistema"
            />

            <div className="mt-6 animate-enter">
              <DataTable
                columns={columns}
                data={moradoresWithActions}
                enableSearch={true}
                searchPlaceholder="Pesquisar moradores..."
                enablePagination={true}
                filters={filters}
                actionLabel="Ações"
                isLoading={isLoading}
                customControls={
                  <div className="flex items-center gap-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowExemptOnly(!showExemptOnly)}
                      className={showExemptOnly ? "bg-yellow-50 border-yellow-200" : ""}
                    >
                      <Shield className="w-4 h-4 mr-2" />
                      {showExemptOnly ? "Mostrar Todos" : "Mostrar Isentos"}
                    </Button>
                    <button
                      onClick={() => setIsModalOpen(true)}
                      className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
                    >
                      <UserPlus size={18} />
                      <span>Adicionar Morador</span>
                    </button>
                  </div>
                }
              />
            </div>

            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
              <DialogContent className="sm:max-w-md max-h-[90vh] flex flex-col">
                <DialogHeader className="flex-shrink-0">
                  <DialogTitle>Adicionar Novo Morador</DialogTitle>
                </DialogHeader>

                <div className="flex-1 overflow-y-auto pr-2 min-h-0">
                  <Form {...newResidentForm}>
                    <form id="new-resident-form" onSubmit={newResidentForm.handleSubmit(handleCreateResident)} className="space-y-4 pb-4">
                      <FormField
                        control={newResidentForm.control}
                        name="nome"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Nome <span className="text-red-500">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Nome completo" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newResidentForm.control}
                        name="apartamento"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Apartamento <span className="text-red-500">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Ex: A101" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newResidentForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>E-mail</FormLabel>
                            <FormControl>
                              <Input {...field} type="email" placeholder="<EMAIL>" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newResidentForm.control}
                        name="telefone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Telefone</FormLabel>
                            <FormControl>
                              <Input {...field} type="tel" placeholder="+244 123 456 789" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="border-t pt-4">
                        <ExemptionManager
                          isExempt={isExempt || false}
                          exemptionReason={newResidentForm.watch('motivo_isencao') || ''}
                          onExemptChange={(exempt) => {
                            newResidentForm.setValue('isento_quotas', exempt);
                            if (!exempt) {
                              newResidentForm.setValue('motivo_isencao', '');
                            }
                          }}
                          onReasonChange={(reason) => newResidentForm.setValue('motivo_isencao', reason)}
                        />
                      </div>
                    </form>
                  </Form>
                </div>

                <DialogFooter className="flex-shrink-0 pt-4 border-t bg-white">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                  >
                    Cancelar
                  </Button>
                  <Button
                    type="submit"
                    form="new-resident-form"
                    disabled={createResidentMutation.isPending}
                  >
                    {createResidentMutation.isPending ? 'Salvando...' : 'Salvar'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {selectedResident && (
              <GrantAccessModal
                isOpen={isGrantAccessModalOpen}
                onClose={handleCloseGrantAccessModal}
                resident={selectedResident}
                onGrantAccess={handleCreateUserAccess}
                isLoading={isCreatingUser}
                error={createUserError}
                status={createUserStatus}
              />
            )}

            <EditResidentModal
              isOpen={isEditModalOpen}
              onClose={() => setIsEditModalOpen(false)}
              resident={selectedResident}
              onSave={handleUpdateResident}
            />

            <AlertDialog open={residentToDelete !== null} onOpenChange={(open) => !open && setResidentToDelete(null)}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Excluir Morador</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja excluir o morador "{residentToDelete?.nome}"?
                    Esta ação não pode ser desfeita.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={confirmDelete}
                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                  >
                    {deleteResidentMutation.isPending ? 'Excluindo...' : 'Excluir'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <AlertDialog open={residentToRevokeAccess !== null} onOpenChange={(open) => !open && setResidentToRevokeAccess(null)}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Revogar Acesso</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja revogar o acesso do morador "{residentToRevokeAccess?.name}"?
                    O morador não conseguirá mais acessar o sistema.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={confirmRevokeAccess}
                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                  >
                    {isRevokingAccess ? 'Revogando...' : 'Revogar Acesso'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <AlertDialog open={residentToRevokeAndDelete !== null} onOpenChange={(open) => !open && setResidentToRevokeAndDelete(null)}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Revogar Acesso e Eliminar Utilizador</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja revogar o acesso e eliminar completamente o utilizador "{residentToRevokeAndDelete?.name}" ({residentToRevokeAndDelete?.email})?
                    <br /><br />
                    <strong>Esta é a opção recomendada</strong> que irá:
                    <br />• Revogar todos os acessos
                    <br />• Eliminar o perfil do utilizador
                    <br />• Remover o utilizador da autenticação
                    <br />• Evitar usuários órfãos no sistema
                    <br /><br />
                    Esta ação não pode ser desfeita.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={confirmRevokeAndDeleteCompletely}
                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                  >
                    {isRevokingAndDeleting ? 'Processando...' : 'Revogar & Eliminar'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <AlertDialog open={residentToDeleteCompletely !== null} onOpenChange={(open) => !open && setResidentToDeleteCompletely(null)}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Limpar Utilizador Órfão</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja limpar o utilizador órfão "{residentToDeleteCompletely?.name}" ({residentToDeleteCompletely?.email})?
                    <br /><br />
                    <strong>Esta opção é para casos especiais</strong> onde o utilizador já teve o acesso revogado mas permanece no sistema de autenticação.
                    <br /><br />
                    Esta ação não pode ser desfeita.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={confirmDeleteUserCompletely}
                    className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                  >
                    {isDeletingUser ? 'Limpando...' : 'Limpar Utilizador'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Residents;
