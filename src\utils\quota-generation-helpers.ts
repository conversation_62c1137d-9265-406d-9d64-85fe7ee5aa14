
import { <PERSON><PERSON><PERSON>, Quota } from '@/types';
import { getConfiguracaoByNome } from '@/utils/supabase-helpers';

export interface QuotaGenerationOptions {
  startMonth: number;
  startYear: number;
  endMonth: number;
  endYear: number;
  includeExempt?: boolean;
}

export const generateQuotasForMoradores = async (
  moradores: Morador[],
  options: QuotaGenerationOptions
): Promise<Omit<Quota, 'id' | 'created_at' | 'updated_at'>[]> => {
  console.log('🔄 Iniciando geração de quotas com opções:', options);
  
  // Buscar valor padrão das configurações
  const valorQuotaConfig = await getConfiguracaoByNome('valor_quota');
  const valorQuota = valorQuotaConfig ? parseFloat(valorQuotaConfig) : 2000;

  // Buscar dia de vencimento das configurações
  const diaVencimentoConfig = await getConfiguracaoByNome('dia_vencimento_quota');
  const diaVencimento = diaVencimentoConfig ? parseInt(diaVencimentoConfig) : 10;

  console.log(`💰 Valor da quota configurado: ${valorQuota} Kz`);
  console.log(`📅 Dia de vencimento configurado: ${diaVencimento}`);

  const quotas: Omit<Quota, 'id' | 'created_at' | 'updated_at'>[] = [];
  const { startMonth, startYear, endMonth, endYear, includeExempt = false } = options;

  // Filtrar moradores se não incluir isentos
  const moradoresAtivos = includeExempt 
    ? moradores 
    : moradores.filter(morador => !morador.isento_quotas);

  console.log(`👥 Total de moradores: ${moradores.length}`);
  console.log(`👥 Moradores isentos: ${moradores.filter(m => m.isento_quotas).length}`);
  console.log(`👥 Moradores para geração: ${moradoresAtivos.length}`);

  if (moradoresAtivos.length === 0) {
    console.log('⚠️ Nenhum morador ativo encontrado para geração de quotas');
    return quotas;
  }

  // Gerar quotas para cada período
  let currentYear = startYear;
  let currentMonth = startMonth;

  while (
    currentYear < endYear || 
    (currentYear === endYear && currentMonth <= endMonth)
  ) {
    console.log(`📅 Gerando quotas para ${currentMonth}/${currentYear}`);

    for (const morador of moradoresAtivos) {
      if (morador.isento_quotas && !includeExempt) {
        console.log(`⏭️ Pulando morador isento: ${morador.nome} (${morador.apartamento})`);
        continue;
      }

      // Calcular data de vencimento usando configuração (padrão dia 10)
      // Usar Date.UTC para evitar problemas de timezone
      const dataVencimento = new Date(Date.UTC(currentYear, currentMonth - 1, diaVencimento));
      
      const quota: Omit<Quota, 'id' | 'created_at' | 'updated_at'> = {
        morador_id: morador.id,
        mes: currentMonth,
        ano: currentYear,
        valor: valorQuota,
        status: 'Não Pago',
        data_vencimento: dataVencimento.toISOString().split('T')[0],
        data_pagamento: null,
        multa: 0,
        numero_multas: 0,
        situacao: 'Não Regularizada',
        comprovativo: null
      };

      quotas.push(quota);
      
      if (morador.isento_quotas) {
        console.log(`⚠️ Quota gerada para morador isento como exceção: ${morador.nome} (${morador.apartamento})`);
      }
    }

    // Avançar para o próximo mês
    currentMonth++;
    if (currentMonth > 12) {
      currentMonth = 1;
      currentYear++;
    }
  }

  console.log(`✅ Total de quotas geradas: ${quotas.length}`);
  
  return quotas;
};

// Função para geração retroativa (mantida para compatibilidade)
export const generateRetroactiveQuotas = async (
  moradores: Morador[]
): Promise<Omit<Quota, 'id' | 'created_at' | 'updated_at'>[]> => {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const currentYear = currentDate.getFullYear();

  return generateQuotasForMoradores(moradores, {
    startMonth: 1,
    startYear: 2024,
    endMonth: currentMonth,
    endYear: currentYear,
    includeExempt: false
  });
};

// Função para geração de período personalizado
export const generateCustomPeriodQuotas = async (
  moradores: Morador[],
  startMonth: number,
  startYear: number,
  includeExempt: boolean = false
): Promise<Omit<Quota, 'id' | 'created_at' | 'updated_at'>[]> => {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;
  const currentYear = currentDate.getFullYear();

  return generateQuotasForMoradores(moradores, {
    startMonth,
    startYear,
    endMonth: currentMonth,
    endYear: currentYear,
    includeExempt
  });
};
