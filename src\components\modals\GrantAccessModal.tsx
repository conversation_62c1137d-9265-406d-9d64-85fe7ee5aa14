import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { generateSecurePassword, sendCredentialsEmail } from '@/utils/resident-access-helpers';
import { toast } from 'sonner';
import { GrantAccessModalProps, GrantAccessFormValues, SuccessCredentials, grantAccessSchema } from './grant-access/types';
import { ConfigurationForm } from './grant-access/ConfigurationForm';
import { CredentialsDisplay } from './grant-access/CredentialsDisplay';

export const GrantAccessModal: React.FC<GrantAccessModalProps> = ({
  isOpen,
  onClose,
  resident,
  onGrantAccess,
  isLoading = false,
  error = null,
  status = 'idle'
}) => {
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [successCredentials, setSuccessCredentials] = useState<SuccessCredentials | null>(null);
  const [activeTab, setActiveTab] = useState('config');
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);
  const [wasOperationSuccessful, setWasOperationSuccessful] = useState(false);
  const [operationStartTime, setOperationStartTime] = useState<number | null>(null);
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const [errorDetails, setErrorDetails] = useState<any>(null);

  const form = useForm<GrantAccessFormValues>({
    resolver: zodResolver(grantAccessSchema),
    defaultValues: {
      email: resident.email,
      useGeneratedPassword: true,
      customPassword: '',
      sendEmailToResident: true,
    }
  });

  const useGeneratedPassword = form.watch('useGeneratedPassword');

  React.useEffect(() => {
    if (useGeneratedPassword && !generatedPassword) {
      const password = generateSecurePassword();
      setGeneratedPassword(password);
      console.log('🔑 [GrantAccessModal] Generated password for resident:', password);
    }
  }, [useGeneratedPassword, generatedPassword]);

  // Set resident email automatically when modal opens
  React.useEffect(() => {
    if (isOpen && resident?.email) {
      form.setValue('email', resident.email);
      console.log('📧 [GrantAccessModal] Set resident email:', resident.email);
    }
  }, [isOpen, resident?.email, form]);

  React.useEffect(() => {
    console.log('🔍 [GrantAccessModal] Props changed:', {
      isOpen,
      isLoading,
      resident: resident?.nome,
      apartment: resident?.apartamento,
      isProcessing,
      lastError,
      wasOperationSuccessful
    });
  }, [isOpen, isLoading, resident, isProcessing, lastError, wasOperationSuccessful]);

  React.useEffect(() => {
    if (error && isProcessing) {
      console.log('🚨 [GrantAccessModal] Error detected during processing:', error.message);
      setLastError(error.message);
      setErrorDetails({
        timestamp: new Date().toISOString(),
        errorMessage: error.message,
        operationDuration: operationStartTime ? Date.now() - operationStartTime : 0
      });
    }
  }, [error, isProcessing, operationStartTime]);

  const handleSubmit = (data: GrantAccessFormValues) => {
    console.log('📝 [GrantAccessModal] Form submit triggered!');
    console.log('📝 [GrantAccessModal] Form data:', data);

    if (isProcessing || isLoading) {
      console.log('⚠️ [GrantAccessModal] Already processing, ignoring submit');
      return;
    }

    setLastError(null);
    setWasOperationSuccessful(false);
    setSuccessCredentials(null);
    setIsProcessing(true);
    setOperationStartTime(Date.now());

    const password = data.useGeneratedPassword ? generatedPassword : data.customPassword!;

    if (!password) {
      console.error('❌ [GrantAccessModal] No password available');
      toast.error('Erro: Senha não definida');
      setIsProcessing(false);
      setOperationStartTime(null);
      return;
    }

    const params = {
      email: resident.email,
      password,
      name: resident.nome,
      apartment_id: resident.apartamento
    };

    console.log('🚀 [GrantAccessModal] Calling onGrantAccess with params:', params);

    try {
      onGrantAccess(params);
      console.log('✅ [GrantAccessModal] onGrantAccess called successfully');
    } catch (error) {
      console.error('❌ [GrantAccessModal] Error calling onGrantAccess:', error);
      setIsProcessing(false);
      setOperationStartTime(null);
      setLastError(error instanceof Error ? error.message : 'Erro desconhecido');
      toast.error('Erro ao processar solicitação');
    }
  };

  React.useEffect(() => {
    console.log('🔍 [GrantAccessModal] Operation state changed:', {
      isLoading,
      isProcessing,
      status,
      hasError: !!error,
      errorMessage: error?.message
    });

    if (isProcessing && !isLoading) {
      console.log('✅ [GrantAccessModal] Operation completed with status:', status);
      setIsProcessing(false);

      if (status === 'success') {
        console.log('✅ [GrantAccessModal] Operation successful');
        setWasOperationSuccessful(true);
        setLastError(null);

        const passwordToUse = form.getValues('useGeneratedPassword') ? generatedPassword : form.getValues('customPassword')!;

        const credentials: SuccessCredentials = {
          email: resident.email,
          password: passwordToUse,
          name: resident.nome,
          apartment: resident.apartamento
        };

        setSuccessCredentials(credentials);
        console.log('🔑 [GrantAccessModal] Credentials set:', credentials);

        toast.success(`Acesso criado para ${resident.nome}!`);

        const formData = form.getValues();
        if (formData.sendEmailToResident) {
          console.log('📧 [GrantAccessModal] Sending credentials email...');
          toast.loading('Enviando email...', { id: 'email-sending' });

          sendCredentialsEmail(credentials, formData.sendEmailToResident)
            .then((result) => {
              toast.dismiss('email-sending');
              if (result === true) {
                console.log('✅ [GrantAccessModal] Email sent successfully');
                toast.success('Email enviado com sucesso!');
              } else if (result === 'simulated') {
                console.warn('⚠️ [GrantAccessModal] Email was simulated');
                toast.info('Email simulado (configurar RESEND_API_KEY para envio real)');
              } else {
                console.warn('⚠️ [GrantAccessModal] Email sending failed');
                toast.warning('Houve problema no envio do email');
              }
            })
            .catch((error) => {
              toast.dismiss('email-sending');
              console.error('❌ [GrantAccessModal] Email sending error:', error);
              toast.warning('Houve problema no envio do email');
            });
        }

        console.log('🔄 [GrantAccessModal] Switching to credentials tab');
        setActiveTab('credentials');
      } else if (status === 'error' && error) {
        console.log('❌ [GrantAccessModal] Operation failed with error:', error.message);
        setWasOperationSuccessful(false);
        setLastError(error.message);
        setSuccessCredentials(null);
        setErrorDetails({
          timestamp: new Date().toISOString(),
          errorMessage: error.message,
          operationDuration: operationStartTime ? Date.now() - operationStartTime : 0
        });
      }
    }
  }, [isLoading, isProcessing, status, error, generatedPassword, resident, form, operationStartTime]);

  React.useEffect(() => {
    if (!isOpen) {
      console.log('🚪 [GrantAccessModal] Modal closed, resetting all state');
      setSuccessCredentials(null);
      setActiveTab('config');
      setGeneratedPassword('');
      setIsProcessing(false);
      setLastError(null);
      setWasOperationSuccessful(false);
      form.reset({
        email: '',
        useGeneratedPassword: true,
        sendEmailToResident: false
      });
    }
  }, [isOpen, form]);

  const handleClose = () => {
    console.log('🚪 [GrantAccessModal] Modal closing, resetting state');
    setSuccessCredentials(null);
    setActiveTab('config');
    setGeneratedPassword('');
    setIsProcessing(false);
    setLastError(null);
    setWasOperationSuccessful(false);
    form.reset({
      email: '',
      useGeneratedPassword: true,
      sendEmailToResident: false
    });
    onClose();
  };

  const handleFormSubmitWrapper = (e: React.FormEvent) => {
    console.log('🎯 [GrantAccessModal] Form submit event triggered');
    e.preventDefault();
    
    form.clearErrors();
    setLastError(null);
    
    form.handleSubmit(
      (data) => {
        console.log('✅ [GrantAccessModal] Form validation passed, calling handleSubmit');
        handleSubmit(data);
      },
      (errors) => {
        console.error('❌ [GrantAccessModal] Form validation failed:', errors);
        toast.error('Por favor, corrija os erros no formulário');
      }
    )(e);
  };

  console.log('🔍 [GrantAccessModal] Render - isOpen:', isOpen, 'isLoading:', isLoading, 'isProcessing:', isProcessing, 'activeTab:', activeTab, 'lastError:', lastError, 'wasOperationSuccessful:', wasOperationSuccessful);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        handleClose();
      }
    }}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>
            Conceder Acesso - {resident.nome}
          </DialogTitle>
        </DialogHeader>

        {lastError && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            <div className="flex justify-between items-start">
              <div>
                <strong>Erro:</strong> {lastError}
              </div>
              {errorDetails && (
                <button
                  onClick={() => setShowErrorDetails(!showErrorDetails)}
                  className="text-red-600 hover:text-red-800 text-sm underline ml-2"
                >
                  {showErrorDetails ? 'Ocultar' : 'Ver Detalhes'}
                </button>
              )}
            </div>
            {showErrorDetails && errorDetails && (
              <div className="mt-3 p-3 bg-red-100 rounded text-xs">
                <div><strong>Timestamp:</strong> {errorDetails.timestamp}</div>
                <div><strong>Duração:</strong> {errorDetails.operationDuration}ms</div>
                {errorDetails.toastContent && (
                  <div><strong>Toast:</strong> {errorDetails.toastContent}</div>
                )}
                <div className="mt-2">
                  <strong>Diagnóstico:</strong> Verifique os logs do navegador (F12) para mais detalhes
                </div>
              </div>
            )}
          </div>
        )}

        <div className="flex-1 overflow-y-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="config">Configuração</TabsTrigger>
              <TabsTrigger value="credentials" disabled={!wasOperationSuccessful || !successCredentials || !!lastError}>
                Credenciais
              </TabsTrigger>
            </TabsList>

            <TabsContent value="config" className="space-y-4">
              <ConfigurationForm
                form={form}
                resident={resident}
                generatedPassword={generatedPassword}
                setGeneratedPassword={setGeneratedPassword}
                onSubmit={handleFormSubmitWrapper}
                onClose={handleClose}
                isLoading={isLoading}
                isProcessing={isProcessing}
              />
            </TabsContent>

            <TabsContent value="credentials" className="space-y-4">
              {successCredentials && wasOperationSuccessful && !lastError && (
                <CredentialsDisplay
                  credentials={successCredentials}
                  sendEmailToResident={form.getValues('sendEmailToResident')}
                  onClose={handleClose}
                />
              )}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
