// 🧪 TESTE RÁPIDO DA LÓGICA DE MULTAS
// Cole este código completo no console do navegador

// Simular as funções de cálculo (versão simplificada)
function calculateFineAmount(dataPagamento, dataVencimento, config) {
  if (!dataPagamento) return 0;
  
  const vencimento = new Date(dataVencimento);
  const pagamento = new Date(dataPagamento);
  
  // Calcular diferença em dias
  const diffTime = pagamento.getTime() - vencimento.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  console.log(`📅 Diferença: ${diffDays} dias (pagamento vs vencimento)`);
  
  // Se pagou após o vencimento + tolerância
  if (diffDays > config.diasAtrasoMulta) {
    console.log(`💰 Aplicando multa: ${config.valorMulta} Kz`);
    return config.valorMulta;
  }
  
  console.log('✅ Sem multa - pagamento dentro do prazo');
  return 0;
}

// Configuração de teste
const testConfig = {
  valorMulta: 1000,
  diasAtrasoMulta: 0
};

console.log('🧪 INICIANDO TESTE DA LÓGICA DE MULTAS');
console.log('====================================');

// Teste 1: Pagamento ANTES do limite (problema identificado)
console.log('\n📅 TESTE 1: Pagamento ANTES do limite');
const dataVencimento1 = '2025-06-10';
const dataPagamento1 = '2025-06-02';
const multa1 = calculateFineAmount(dataPagamento1, dataVencimento1, testConfig);

console.log(`Data vencimento: ${dataVencimento1}`);
console.log(`Data pagamento: ${dataPagamento1}`);
console.log(`Resultado: ${multa1} Kz`);
console.log(`✅ ESPERADO: 0 Kz | ${multa1 === 0 ? '✅ PASSOU' : '❌ FALHOU'}`);

// Teste 2: Pagamento APÓS o limite
console.log('\n📅 TESTE 2: Pagamento APÓS o limite');
const dataVencimento2 = '2025-06-10';
const dataPagamento2 = '2025-06-15';
const multa2 = calculateFineAmount(dataPagamento2, dataVencimento2, testConfig);

console.log(`Data vencimento: ${dataVencimento2}`);
console.log(`Data pagamento: ${dataPagamento2}`);
console.log(`Resultado: ${multa2} Kz`);
console.log(`✅ ESPERADO: 1000 Kz | ${multa2 === 1000 ? '✅ PASSOU' : '❌ FALHOU'}`);

// Teste 3: Pagamento EXATAMENTE no limite
console.log('\n📅 TESTE 3: Pagamento EXATAMENTE no limite');
const dataVencimento3 = '2025-06-10';
const dataPagamento3 = '2025-06-10';
const multa3 = calculateFineAmount(dataPagamento3, dataVencimento3, testConfig);

console.log(`Data vencimento: ${dataVencimento3}`);
console.log(`Data pagamento: ${dataPagamento3}`);
console.log(`Resultado: ${multa3} Kz`);
console.log(`✅ ESPERADO: 0 Kz | ${multa3 === 0 ? '✅ PASSOU' : '❌ FALHOU'}`);

// Resumo
const testes = [
  { nome: 'Pagamento antes do limite', passou: multa1 === 0 },
  { nome: 'Pagamento após o limite', passou: multa2 === 1000 },
  { nome: 'Pagamento no limite', passou: multa3 === 0 }
];

const testesPassaram = testes.filter(t => t.passou).length;
const totalTestes = testes.length;

console.log('\n🎯 RESUMO DOS TESTES');
console.log('===================');
testes.forEach(teste => {
  console.log(`${teste.passou ? '✅' : '❌'} ${teste.nome}`);
});

console.log(`\n📊 RESULTADO: ${testesPassaram}/${totalTestes} testes passaram`);

if (testesPassaram === totalTestes) {
  console.log('🎉 TODOS OS TESTES PASSARAM! A lógica está correta.');
} else {
  console.log('⚠️ ALGUNS TESTES FALHARAM! Há problemas na lógica.');
}

// Teste com dados reais do seu sistema
console.log('\n🔍 TESTE COM DADOS REAIS DO SEU SISTEMA');
console.log('======================================');
console.log('Quota do Ferreira João (1ºD):');
console.log('- Data vencimento: 2025-06-10');
console.log('- Data pagamento: 2025-06-02 (ANTES do limite)');
console.log('- Multa atual no sistema: 1000 Kz ❌');

const multaCorreta = calculateFineAmount('2025-06-02', '2025-06-10', testConfig);
console.log(`- Multa que deveria ser: ${multaCorreta} Kz ✅`);
console.log(`- Diferença: ${1000 - multaCorreta} Kz a menos`);

console.log('\n💡 CONCLUSÃO: A correção implementada vai resolver este problema!');
