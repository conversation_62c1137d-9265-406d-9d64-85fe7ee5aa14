
/**
 * Componente Chart - Gráficos Responsivos para Dashboard
 *
 * Componente versátil que suporta múltiplos tipos de gráficos (área, barras, linha, pizza)
 * usando a biblioteca Recharts. Inclui formatação de moeda angolana e estados de carregamento.
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import React from 'react';
import { AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { cn } from '@/lib/utils';

// Tipos de gráficos suportados
type ChartType = 'area' | 'bar' | 'line' | 'pie';

/**
 * Interface para as propriedades do componente Chart
 */
interface ChartProps {
  type: ChartType;                  // Tipo do gráfico
  data: any[];                      // Dados para renderização
  height?: number;                  // Altura do gráfico em pixels
  title?: string;                   // Título do gráfico
  subtitle?: string;                // Subtítulo do gráfico
  className?: string;               // Classes CSS adicionais
  colors?: string[];                // Paleta de cores personalizada
  dataKeys?: string[];              // Chaves dos dados a serem exibidos
  showGrid?: boolean;               // Exibir grade de fundo
  showLegend?: boolean;             // Exibir legenda
  innerRadius?: number;             // Raio interno (para gráficos de pizza)
  outerRadius?: number;             // Raio externo (para gráficos de pizza)
  isLoading?: boolean;              // Estado de carregamento
}

/**
 * Paleta de cores padrão do sistema
 * Baseada no design system do Prédio Azul
 */
const defaultColors = [
  '#019cdf', // Azul primário
  '#66cbfb', // Azul claro (primary-300)
  '#33bafa', // Azul médio (primary-400)
  '#017db2', // Azul escuro (primary-600)
  '#015e86', // Azul muito escuro (primary-700)
  '#45B69C', // Verde teal
  '#6670fd', // Índigo
  '#FDA349'  // Laranja
];

/**
 * Componente funcional Chart
 *
 * Renderiza diferentes tipos de gráficos com configurações personalizáveis
 * e formatação específica para dados financeiros em Kwanzas.
 *
 * @param props - Propriedades do componente
 * @returns {JSX.Element} Gráfico renderizado
 */
const Chart: React.FC<ChartProps> = ({
  type,                             // Tipo do gráfico
  data,                             // Dados para renderização
  height = 300,                     // Altura padrão: 300px
  title,                            // Título opcional
  subtitle,                         // Subtítulo opcional
  className,                        // Classes CSS extras
  colors = defaultColors,           // Cores padrão do sistema
  dataKeys = ['value'],             // Chave padrão dos dados
  showGrid = true,                  // Grade habilitada por padrão
  showLegend = true,                // Legenda habilitada por padrão
  innerRadius = 60,                 // Raio interno padrão para pizza
  outerRadius = 80,                 // Raio externo padrão para pizza
  isLoading = false                 // Estado de carregamento
}) => {
  /**
   * Formata valores monetários para o padrão angolano
   * @param {number} value - Valor a ser formatado
   * @returns {string} Valor formatado em Kwanzas
   */
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value).replace('AOA', 'Kz');
  };

  /**
   * Formatador personalizado para tooltips
   * Formata valores baseado no tipo de dados (monetário ou contagem)
   * @param {any} value - Valor a ser exibido
   * @param {string} name - Nome do campo
   * @returns {[string, string]} Array com valor formatado e nome traduzido
   */
  const tooltipFormatter = (value: any, name: string) => {
    // Para gráficos de quotas (contagem), não formatar como moeda
    if (name === 'pagas' || name === 'pendentes') {
      return [
        `${value} quota${value !== 1 ? 's' : ''}`,
        name === 'pagas' ? 'Pagas' : 'Pendentes'
      ];
    }

    // Para gráficos financeiros, formatar como moeda
    return [
      formatCurrency(value),
      name === 'entrada' ? 'Entrada' : name === 'saida' ? 'Saída' : name
    ];
  };
  // Loading skeleton
  if (isLoading) {
    return (
      <div className={cn("dashboard-card", className)}>
        {(title || subtitle) && (
          <div className="mb-4">
            {title && <div className="animate-pulse bg-gray-200 rounded h-5 w-32 mb-2" />}
            {subtitle && <div className="animate-pulse bg-gray-200 rounded h-4 w-48" />}
          </div>
        )}
        <div
          className="animate-pulse bg-gray-200 rounded w-full"
          style={{ height: `${height}px` }}
        />
      </div>
    );
  }
  // Function to render the appropriate chart type
  const renderChart = () => {
    switch (type) {
      case 'area':
        return <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={data} margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0
          }}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
              <XAxis dataKey="name" tick={{
              fontSize: 12,
              fill: '#6b7280'
            }} tickLine={{
              stroke: '#e5e7eb'
            }} axisLine={{
              stroke: '#e5e7eb'
            }} />
              <YAxis tick={{
              fontSize: 12,
              fill: '#6b7280'
            }} tickLine={{
              stroke: '#e5e7eb'
            }} axisLine={{
              stroke: '#e5e7eb'
            }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #f3f4f6'
                }}
                formatter={tooltipFormatter}
              />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => <Area key={key} type="monotone" dataKey={key} stroke={colors[index % colors.length]} fill={`${colors[index % colors.length]}20`} activeDot={{
              r: 6,
              strokeWidth: 1,
              stroke: 'white'
            }} />)}
            </AreaChart>
          </ResponsiveContainer>;
      case 'bar':
        return <ResponsiveContainer width="100%" height={height}>
            <BarChart data={data} margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0
          }}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
              <XAxis dataKey="name" tick={{
              fontSize: 12,
              fill: '#6b7280'
            }} tickLine={{
              stroke: '#e5e7eb'
            }} axisLine={{
              stroke: '#e5e7eb'
            }} />
              <YAxis tick={{
              fontSize: 12,
              fill: '#6b7280'
            }} tickLine={{
              stroke: '#e5e7eb'
            }} axisLine={{
              stroke: '#e5e7eb'
            }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #f3f4f6'
                }}
                formatter={tooltipFormatter}
              />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => <Bar key={key} dataKey={key} fill={colors[index % colors.length]} radius={[4, 4, 0, 0]} barSize={40} />)}
            </BarChart>
          </ResponsiveContainer>;
      case 'line':
        return <ResponsiveContainer width="100%" height={height}>
            <LineChart data={data} margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0
          }}>
              {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
              <XAxis dataKey="name" tick={{
              fontSize: 12,
              fill: '#6b7280'
            }} tickLine={{
              stroke: '#e5e7eb'
            }} axisLine={{
              stroke: '#e5e7eb'
            }} />
              <YAxis tick={{
              fontSize: 12,
              fill: '#6b7280'
            }} tickLine={{
              stroke: '#e5e7eb'
            }} axisLine={{
              stroke: '#e5e7eb'
            }} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #f3f4f6'
                }}
                formatter={tooltipFormatter}
              />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => <Line key={key} type="monotone" dataKey={key} stroke={colors[index % colors.length]} strokeWidth={2} dot={{
              stroke: colors[index % colors.length],
              strokeWidth: 2,
              r: 4,
              fill: 'white'
            }} activeDot={{
              r: 6,
              strokeWidth: 1,
              stroke: 'white'
            }} />)}
            </LineChart>
          </ResponsiveContainer>;
      case 'pie':
        return <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" labelLine={false} label={({
              name,
              percent
            }) => `${name}: ${(percent * 100).toFixed(0)}%`} outerRadius={outerRadius} innerRadius={innerRadius} dataKey={dataKeys[0]}>
                {data.map((entry, index) => <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />)}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  borderRadius: 8,
                  boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #f3f4f6'
                }}
                formatter={tooltipFormatter}
              />
              {showLegend && <Legend />}
            </PieChart>
          </ResponsiveContainer>;
      default:
        return <p>Chart type not supported</p>;
    }
  };

  return (
    <div className={cn("chart-container", className)}>
      {(title || subtitle) && (
        <div className="chart-header mb-4">
          {title && <h3 className="text-lg font-semibold text-gray-800">{title}</h3>}
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
        </div>
      )}
      {renderChart()}
    </div>
  );
};

export default Chart;
