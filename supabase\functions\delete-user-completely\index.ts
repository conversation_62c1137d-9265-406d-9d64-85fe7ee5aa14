
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface DeleteUserRequest {
  email: string;
}

Deno.serve(async (req) => {
  console.log('🚀 [DEBUG] Delete user completely function started');
  console.log('🔍 [DEBUG] Request method:', req.method);

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('✅ [DEBUG] Handling CORS preflight request');
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get and validate request body
    const requestBody = await req.json();
    const { email }: DeleteUserRequest = requestBody;
    
    console.log('📋 [DEBUG] Delete request for email:', email);

    if (!email) {
      console.error('❌ [DEBUG] Missing email parameter');
      return new Response(
        JSON.stringify({ error: 'Email is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SERVICE_ROLE_KEY');

    if (!serviceRoleKey || !supabaseUrl) {
      console.error('❌ [DEBUG] Missing environment variables');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create Supabase admin client
    const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Get current user from the request (the admin who is deleting the user)
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('❌ [DEBUG] No authorization header found');
      return new Response(
        JSON.stringify({ error: 'Authorization required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create a regular client to verify the current user is admin
    const supabaseAnon = createClient(
      supabaseUrl,
      Deno.env.get('SUPABASE_ANON_KEY')!
    );

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAnon.auth.getUser(token);

    if (userError || !user) {
      console.error('❌ [DEBUG] Invalid token or user not found:', userError);
      return new Response(
        JSON.stringify({ error: 'Invalid authentication token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if the current user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (profileError || !profile || profile.role !== 'admin') {
      console.error('❌ [DEBUG] User is not admin');
      return new Response(
        JSON.stringify({ error: 'Admin privileges required' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('✅ [DEBUG] User verified as admin, proceeding with deletion...');

    // Step 1: Find user by email in profiles table
    console.log('🔍 [DEBUG] Finding user in profiles table...');
    const { data: userProfile, error: findError } = await supabaseAdmin
      .from('profiles')
      .select('id, email, name')
      .eq('email', email)
      .maybeSingle();

    if (findError) {
      console.error('❌ [DEBUG] Error finding user profile:', findError);
      return new Response(
        JSON.stringify({ error: 'Error finding user profile' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    let deletedFromAuth = false;
    let deletedProfile = false;
    let deletedAccess = false;

    // Step 2: Delete from auth.users if exists
    if (userProfile?.id) {
      console.log('🗑️ [DEBUG] Deleting user from auth.users...');
      try {
        const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(userProfile.id);
        if (authDeleteError) {
          console.warn('⚠️ [DEBUG] Could not delete from auth.users (may not exist):', authDeleteError.message);
        } else {
          deletedFromAuth = true;
          console.log('✅ [DEBUG] Deleted from auth.users');
        }
      } catch (authError) {
        console.warn('⚠️ [DEBUG] Auth deletion failed (user may not exist in auth):', authError);
      }

      // Step 3: Delete apartment access
      console.log('🗑️ [DEBUG] Deleting apartment access...');
      const { error: accessDeleteError } = await supabaseAdmin
        .from('apartment_access')
        .delete()
        .eq('user_id', userProfile.id);

      if (accessDeleteError) {
        console.warn('⚠️ [DEBUG] Could not delete apartment access:', accessDeleteError.message);
      } else {
        deletedAccess = true;
        console.log('✅ [DEBUG] Deleted apartment access');
      }

      // Step 4: Delete profile
      console.log('🗑️ [DEBUG] Deleting user profile...');
      const { error: profileDeleteError } = await supabaseAdmin
        .from('profiles')
        .delete()
        .eq('id', userProfile.id);

      if (profileDeleteError) {
        console.error('❌ [DEBUG] Could not delete user profile:', profileDeleteError);
        return new Response(
          JSON.stringify({ error: 'Failed to delete user profile' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      } else {
        deletedProfile = true;
        console.log('✅ [DEBUG] Deleted user profile');
      }
    } else {
      // User doesn't exist in profiles, but might exist in auth.users
      console.log('🔍 [DEBUG] User not found in profiles, checking auth.users...');
      
      try {
        // Try to find user in auth.users by email
        const { data: authUsers, error: listUsersError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (!listUsersError && authUsers) {
          const authUser = authUsers.users.find(u => u.email === email);
          
          if (authUser) {
            console.log('🗑️ [DEBUG] Found user in auth.users, deleting...');
            const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(authUser.id);
            
            if (authDeleteError) {
              console.error('❌ [DEBUG] Failed to delete from auth.users:', authDeleteError);
              return new Response(
                JSON.stringify({ error: 'Failed to delete user from authentication' }),
                { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
              );
            } else {
              deletedFromAuth = true;
              console.log('✅ [DEBUG] Deleted orphaned user from auth.users');
            }
          } else {
            console.log('⚠️ [DEBUG] User not found in auth.users either');
          }
        }
      } catch (authListError) {
        console.warn('⚠️ [DEBUG] Could not list auth users:', authListError);
      }
    }

    // If no deletions were made, user probably doesn't exist
    if (!deletedFromAuth && !deletedProfile && !deletedAccess) {
      console.log('⚠️ [DEBUG] User not found in any table');
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'User not found (may already be deleted)',
          email 
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const result = {
      success: true,
      message: 'User deleted successfully',
      email,
      deletions: {
        auth: deletedFromAuth,
        profile: deletedProfile,
        access: deletedAccess
      }
    };

    console.log('🎉 [DEBUG] User deletion completed:', result);

    return new Response(
      JSON.stringify(result),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error: any) {
    console.error('💥 [DEBUG] Unexpected error in delete-user-completely function:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
