
import { useState } from 'react';
import { FluxoCaixa } from '@/types';

type Filters = {
  tipo: string | null;
  mes: string | null;
  ano: string | null;
};

export const useFluxoCaixaFilters = (fluxoCaixa: FluxoCaixa[] | undefined) => {
  const [filters, setFilters] = useState<Filters>({
    tipo: null,
    mes: null,
    ano: null,
  });

  const filteredFluxoCaixa = fluxoCaixa ? fluxoCaixa.filter(item => {
    if (filters.tipo && item.tipo !== filters.tipo) {
      return false;
    }

    if (filters.mes) {
      const date = new Date(item.data);
      const month = String(date.getMonth() + 1).padStart(2, '0');
      if (month !== filters.mes) {
        return false;
      }
    }

    if (filters.ano) {
      const date = new Date(item.data);
      const year = String(date.getFullYear());
      if (year !== filters.ano) {
        return false;
      }
    }

    return true;
  }) : [];

  const handleFilterChange = (filterType: keyof Filters, value: string | null) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  return {
    filters,
    filteredFluxoCaixa,
    handleFilterChange
  };
};
