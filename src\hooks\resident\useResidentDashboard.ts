
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export interface ResidentDashboardData {
  currentQuota: {
    status: 'pending' | 'paid' | 'overdue';
    amount: number;
    dueDate: Date;
    monthYear: string;
    month: number;
    year: number;
  } | null;
  summary: {
    totalPending: number;
    totalFines: number;
    monthsOverdue: number;
    paymentRate: number;
  };
  recentNotifications: {
    id: string;
    title: string;
    message: string;
    type: 'success' | 'warning' | 'info';
    date: string;
  }[];
}

export const useResidentDashboard = () => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['resident-dashboard', profile?.apartment_id],
    queryFn: async (): Promise<ResidentDashboardData> => {
      console.log('🔍 Iniciando busca do dashboard para apartamento:', profile?.apartment_id);
      console.log('🔍 User ID do perfil:', profile?.id);
      
      if (!profile?.apartment_id) {
        console.error('❌ Apartamento não encontrado no perfil');
        throw new Error('Apartamento não encontrado no perfil');
      }

      // Estratégia dupla: buscar morador por apartamento E por user_id
      console.log('🔍 Buscando morador para apartamento:', profile.apartment_id);
      
      // Primeira tentativa: buscar pelo apartamento
      let { data: morador, error: moradorError } = await supabase
        .from('moradores')
        .select('id')
        .eq('apartamento', profile.apartment_id)
        .maybeSingle();

      // Se não encontrou pelo apartamento, tentar pelo user_id
      if (!morador && profile.id) {
        console.log('🔍 Tentando buscar morador pelo user_id:', profile.id);
        const { data: moradorByUserId, error: moradorByUserIdError } = await supabase
          .from('moradores')
          .select('id, apartamento')
          .eq('user_id', profile.id)
          .maybeSingle();

        if (moradorByUserIdError) {
          console.error('❌ Erro ao buscar morador por user_id:', moradorByUserIdError);
        } else if (moradorByUserId) {
          console.log('✅ Morador encontrado pelo user_id:', moradorByUserId);
          morador = moradorByUserId;
          
          // Verificar se o apartamento no perfil está correto
          if (moradorByUserId.apartamento !== profile.apartment_id) {
            console.warn('⚠️ Apartamento no perfil diferente do morador:', {
              perfilApartamento: profile.apartment_id,
              moradorApartamento: moradorByUserId.apartamento
            });
          }
        }
      }

      if (moradorError && !morador) {
        console.error('❌ Erro ao buscar morador:', moradorError);
        throw new Error(`Erro ao buscar morador: ${moradorError.message}`);
      }

      if (!morador) {
        console.warn('⚠️ Morador não encontrado para apartamento:', profile.apartment_id, 'e user_id:', profile.id);
        // Retornar dados vazios mas válidos quando não há morador cadastrado
        return {
          currentQuota: null,
          summary: {
            totalPending: 0,
            totalFines: 0,
            monthsOverdue: 0,
            paymentRate: 100
          },
          recentNotifications: []
        };
      }

      console.log('✅ Morador encontrado:', morador.id);

      // Buscar quota atual (mês atual)
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();

      console.log('🔍 Buscando quota atual para:', { month: currentMonth, year: currentYear });
      const { data: currentQuota, error: quotaError } = await supabase
        .from('quotas')
        .select('*')
        .eq('morador_id', morador.id)
        .eq('mes', currentMonth)
        .eq('ano', currentYear)
        .maybeSingle();

      if (quotaError) {
        console.error('❌ Erro ao buscar quota atual:', quotaError);
      }

      // Buscar resumo de quotas
      console.log('🔍 Buscando todas as quotas do morador');
      const { data: allQuotas, error: allQuotasError } = await supabase
        .from('quotas')
        .select('*')
        .eq('morador_id', morador.id);

      if (allQuotasError) {
        console.error('❌ Erro ao buscar todas as quotas:', allQuotasError);
      }

      // Calcular estatísticas
      const quotas = allQuotas || [];
      const totalPending = quotas.filter(q => q.status === 'Não Pago').reduce((sum, q) => sum + Number(q.valor), 0);
      const totalFines = quotas.reduce((sum, q) => sum + (Number(q.multa) || 0), 0);
      const monthsOverdue = quotas.filter(q => q.status === 'Não Pago' && new Date(q.data_vencimento) < new Date()).length;
      const paidQuotas = quotas.filter(q => q.status === 'Pago').length;
      const totalQuotas = quotas.length;
      const paymentRate = totalQuotas > 0 ? Math.round((paidQuotas / totalQuotas) * 100) : 100;

      console.log('📊 Estatísticas calculadas:', {
        totalPending,
        totalFines,
        monthsOverdue,
        paymentRate,
        totalQuotas
      });

      // Buscar notificações recentes
      console.log('🔍 Buscando notificações recentes para usuário:', profile.id);
      const { data: notifications, error: notificationsError } = await supabase
        .from('notificacoes')
        .select('*')
        .eq('usuario_id', profile.id)
        .order('created_at', { ascending: false })
        .limit(3);

      if (notificationsError) {
        console.error('❌ Erro ao buscar notificações:', notificationsError);
      }

      // Processar quota atual
      const processedCurrentQuota = currentQuota ? {
        status: currentQuota.status === 'Pago' ? 'paid' as const : 
                new Date(currentQuota.data_vencimento) < new Date() ? 'overdue' as const : 'pending' as const,
        amount: Number(currentQuota.valor),
        dueDate: new Date(currentQuota.data_vencimento),
        monthYear: `${getMonthName(currentQuota.mes)}/${currentQuota.ano}`,
        month: currentQuota.mes,
        year: currentQuota.ano
      } : null;

      // Processar notificações
      const recentNotifications = (notifications || []).map(notif => ({
        id: notif.id,
        title: notif.titulo,
        message: notif.mensagem,
        type: notif.tipo as 'success' | 'warning' | 'info',
        date: notif.created_at
      }));

      const dashboardData = {
        currentQuota: processedCurrentQuota,
        summary: {
          totalPending,
          totalFines,
          monthsOverdue,
          paymentRate
        },
        recentNotifications
      };

      console.log('✅ Dashboard data preparado:', dashboardData);
      return dashboardData;
    },
    enabled: !!profile?.apartment_id && !!profile?.id,
    retry: 1,
    refetchOnWindowFocus: false,
  });
};

const getMonthName = (month: number) => {
  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];
  return months[month - 1];
};
