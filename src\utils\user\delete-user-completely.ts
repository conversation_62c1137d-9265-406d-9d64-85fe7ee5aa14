
import { supabase } from '@/integrations/supabase/client';

export const deleteUserCompletely = async (email: string): Promise<boolean> => {
  try {
    console.log('🗑️ Attempting to delete user completely:', email);
    
    const { data, error } = await supabase.rpc('delete_user_completely', {
      p_email: email
    });
    
    if (error) {
      console.error('❌ Error deleting user completely:', error);
      throw error;
    }

    console.log('✅ User deleted completely:', data);
    return data;
  } catch (error) {
    console.error('❌ Error in deleteUserCompletely:', error);
    throw error;
  }
};

export const getUserDetailsByEmail = async (email: string) => {
  try {
    console.log('🔍 Getting user details by email:', email);
    
    const { data, error } = await supabase.rpc('get_user_details_by_email', {
      p_email: email
    });
    
    if (error) {
      console.error('❌ Error getting user details:', error);
      throw error;
    }

    console.log('✅ User details retrieved:', data);
    return data?.[0] || null;
  } catch (error) {
    console.error('❌ Error in getUserDetailsByEmail:', error);
    throw error;
  }
};
