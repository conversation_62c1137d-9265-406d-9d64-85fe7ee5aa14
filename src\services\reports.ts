/**
 * Serviço de Relatórios - Sistema Prédio Azul
 * 
 * Responsável por buscar dados do backend e gerar relatórios
 * em formato PDF com formatação angolana e logo da empresa.
 */

import { supabase } from '@/integrations/supabase/client';
import {
  ReportType,
  ReportFilters,
  ReportData,
  QuotasReportData,
  ResidentsReportData,
  FinancialReportData,
  CommitteeReportData,
  FinesReportData,
  QuotasReportFilters,
  ResidentsReportFilters,
  FinancialReportFilters,
  CommitteeReportFilters,
  FinesReportFilters
} from '@/types/reports';
import { Morador, Quota, FluxoCaixa, MembroComissao } from '@/types';

/**
 * Formata valor monetário no padrão angolano
 */
export const formatCurrencyAO = (value: number): string => {
  return new Intl.NumberFormat('pt-AO', {
    style: 'currency',
    currency: 'AOA',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value).replace('AOA', 'Kz');
};

/**
 * Formata data no padrão português
 */
export const formatDatePT = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('pt-PT', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

/**
 * Busca dados para relatório de quotas
 */
export const getQuotasReportData = async (filters: QuotasReportFilters): Promise<QuotasReportData> => {
  try {
    console.log('🔍 Buscando dados para relatório de quotas:', filters);

    // Construir query base
    let query = supabase
      .from('quotas')
      .select(`
        *,
        moradores!inner(nome, apartamento, status, isento_quotas)
      `);

    // Aplicar filtros de data
    if (filters.startDate) {
      query = query.gte('data_vencimento', filters.startDate);
    }
    if (filters.endDate) {
      query = query.lte('data_vencimento', filters.endDate);
    }

    // Aplicar filtro de status
    if (filters.status && filters.status !== 'all') {
      switch (filters.status) {
        case 'paid':
          query = query.eq('status', 'Pago');
          break;
        case 'pending':
          query = query.eq('status', 'Não Pago');
          break;
        case 'overdue':
          query = query.eq('status', 'Não Pago').lt('data_vencimento', new Date().toISOString());
          break;
      }
    }

    // Aplicar filtro de apartamento
    if (filters.apartmentNumber) {
      query = query.eq('moradores.apartamento', filters.apartmentNumber);
    }

    const { data: quotasData, error } = await query.order('ano', { ascending: false })
                                                  .order('mes', { ascending: false });

    if (error) throw error;

    // Filtrar moradores isentos se necessário
    const filteredQuotas = filters.includeExempt 
      ? quotasData 
      : quotasData?.filter(q => !q.moradores?.isento_quotas) || [];

    // Calcular resumo
    const totalQuotas = filteredQuotas.length;
    const paidQuotas = filteredQuotas.filter(q => q.status === 'Pago').length;
    const pendingQuotas = totalQuotas - paidQuotas;
    const overdueQuotas = filteredQuotas.filter(q => 
      q.status === 'Não Pago' && new Date(q.data_vencimento) < new Date()
    ).length;

    const totalAmount = filteredQuotas.reduce((sum, q) => sum + (q.valor || 0), 0);
    const paidAmount = filteredQuotas
      .filter(q => q.status === 'Pago')
      .reduce((sum, q) => sum + (q.valor || 0), 0);
    const pendingAmount = totalAmount - paidAmount;
    const finesAmount = filteredQuotas.reduce((sum, q) => sum + (q.multa || 0), 0);

    // Mapear dados das quotas
    const quotas = filteredQuotas.map(q => ({
      id: q.id,
      residentName: q.moradores?.nome || 'N/A',
      apartmentNumber: q.moradores?.apartamento || 'N/A',
      month: q.mes,
      year: q.ano,
      amount: q.valor || 0,
      status: q.status || 'Não Pago',
      dueDate: q.data_vencimento,
      paymentDate: q.data_pagamento || undefined,
      fine: q.multa || 0,
      situation: q.situacao || 'Não Regularizada'
    }));

    return {
      type: 'quotas',
      title: 'Relatório de Quotas',
      subtitle: 'Condomínio Prédio Azul',
      generatedAt: new Date().toISOString(),
      generatedBy: 'Sistema', // TODO: Pegar usuário atual
      filters,
      totalRecords: totalQuotas,
      summary: {
        totalQuotas,
        paidQuotas,
        pendingQuotas,
        overdueQuotas,
        totalAmount,
        paidAmount,
        pendingAmount,
        finesAmount
      },
      quotas
    };

  } catch (error) {
    console.error('❌ Erro ao buscar dados de quotas:', error);
    throw new Error('Erro ao gerar relatório de quotas');
  }
};

/**
 * Busca dados para relatório de moradores
 */
export const getResidentsReportData = async (filters: ResidentsReportFilters): Promise<ResidentsReportData> => {
  try {
    console.log('🔍 Buscando dados para relatório de moradores:', filters);

    let query = supabase.from('moradores').select('*');

    // Aplicar filtros
    if (filters.status && filters.status !== 'all') {
      query = query.eq('status', filters.status === 'active' ? 'Ativo' : 'Inativo');
    }

    if (filters.apartmentNumber) {
      query = query.eq('apartamento', filters.apartmentNumber);
    }

    const { data: residentsData, error } = await query.order('apartamento', { ascending: true });

    if (error) throw error;

    const residents = residentsData || [];

    // Calcular resumo
    const totalResidents = residents.length;
    const activeResidents = residents.filter(r => r.status === 'Ativo').length;
    const inactiveResidents = totalResidents - activeResidents;
    const exemptResidents = residents.filter(r => r.isento_quotas).length;

    // Mapear dados dos moradores
    const mappedResidents = residents.map(r => ({
      id: r.id,
      name: r.nome,
      apartmentNumber: r.apartamento,
      status: r.status || 'Ativo',
      phone: filters.includeContact ? r.telefone : undefined,
      email: filters.includeContact ? r.email : undefined,
      isExempt: r.isento_quotas || false,
      registrationDate: r.created_at || new Date().toISOString()
    }));

    return {
      type: 'residents',
      title: 'Relatório de Moradores',
      subtitle: 'Condomínio Prédio Azul',
      generatedAt: new Date().toISOString(),
      generatedBy: 'Sistema',
      filters,
      totalRecords: totalResidents,
      summary: {
        totalResidents,
        activeResidents,
        inactiveResidents,
        exemptResidents
      },
      residents: mappedResidents
    };

  } catch (error) {
    console.error('❌ Erro ao buscar dados de moradores:', error);
    throw new Error('Erro ao gerar relatório de moradores');
  }
};

/**
 * Busca dados para relatório financeiro
 */
export const getFinancialReportData = async (filters: FinancialReportFilters): Promise<FinancialReportData> => {
  try {
    console.log('🔍 Buscando dados para relatório financeiro:', filters);

    let query = supabase.from('fluxo_caixa').select('*');

    // Aplicar filtros de data
    if (filters.startDate) {
      query = query.gte('data', filters.startDate);
    }
    if (filters.endDate) {
      query = query.lte('data', filters.endDate);
    }

    // Aplicar filtro de tipo
    if (filters.type && filters.type !== 'all') {
      query = query.eq('tipo', filters.type === 'income' ? 'entrada' : 'saida');
    }

    // Aplicar filtro de categoria
    if (filters.category) {
      query = query.eq('categoria', filters.category);
    }

    const { data: transactionsData, error } = await query.order('data', { ascending: false });

    if (error) throw error;

    const transactions = transactionsData || [];

    // Calcular resumo
    const totalIncome = transactions
      .filter(t => t.tipo === 'entrada')
      .reduce((sum, t) => sum + (t.valor || 0), 0);
    
    const totalExpense = transactions
      .filter(t => t.tipo === 'saida')
      .reduce((sum, t) => sum + (t.valor || 0), 0);

    const balance = totalIncome - totalExpense;
    const incomeCount = transactions.filter(t => t.tipo === 'entrada').length;
    const expenseCount = transactions.filter(t => t.tipo === 'saida').length;

    // Mapear transações
    const mappedTransactions = transactions.map(t => ({
      id: t.id,
      type: t.tipo as 'entrada' | 'saida',
      category: t.categoria,
      description: t.descricao,
      amount: t.valor || 0,
      date: t.data,
      responsible: t.responsavel_id || undefined
    }));

    // Quebra mensal se solicitada
    let monthlyBreakdown;
    if (filters.groupByMonth) {
      const monthlyData = new Map<string, { income: number; expense: number }>();
      
      transactions.forEach(t => {
        const monthKey = new Date(t.data).toLocaleDateString('pt-PT', { 
          year: 'numeric', 
          month: 'long' 
        });
        
        if (!monthlyData.has(monthKey)) {
          monthlyData.set(monthKey, { income: 0, expense: 0 });
        }
        
        const monthData = monthlyData.get(monthKey)!;
        if (t.tipo === 'entrada') {
          monthData.income += t.valor || 0;
        } else {
          monthData.expense += t.valor || 0;
        }
      });

      monthlyBreakdown = Array.from(monthlyData.entries()).map(([month, data]) => ({
        month,
        income: data.income,
        expense: data.expense,
        balance: data.income - data.expense
      }));
    }

    return {
      type: 'financial',
      title: 'Relatório Financeiro',
      subtitle: 'Condomínio Prédio Azul',
      generatedAt: new Date().toISOString(),
      generatedBy: 'Sistema',
      filters,
      totalRecords: transactions.length,
      summary: {
        totalIncome,
        totalExpense,
        balance,
        incomeCount,
        expenseCount
      },
      transactions: mappedTransactions,
      monthlyBreakdown
    };

  } catch (error) {
    console.error('❌ Erro ao buscar dados financeiros:', error);
    throw new Error('Erro ao gerar relatório financeiro');
  }
};

/**
 * Busca dados para relatório da comissão
 */
export const getCommitteeReportData = async (filters: CommitteeReportFilters): Promise<CommitteeReportData> => {
  try {
    console.log('🔍 Buscando dados para relatório da comissão:', filters);

    const { data: membersData, error } = await supabase
      .from('membros_comissao')
      .select('*')
      .order('cargo', { ascending: true });

    if (error) throw error;

    const members = membersData || [];

    // Calcular resumo
    const totalMembers = members.length;
    const activeMembers = members.filter(m => m.status === 'Ativo').length;

    // Mapear dados dos membros
    const mappedMembers = members.map(m => ({
      id: m.id,
      name: m.nome,
      position: m.cargo,
      phone: filters.includeContact ? m.telefone : undefined,
      email: filters.includeContact ? m.email : undefined,
      startDate: m.data_inicio || new Date().toISOString(),
      endDate: m.data_fim || undefined,
      status: m.status || 'Ativo'
    }));

    return {
      type: 'committee',
      title: 'Relatório da Comissão',
      subtitle: 'Condomínio Prédio Azul',
      generatedAt: new Date().toISOString(),
      generatedBy: 'Sistema',
      filters,
      totalRecords: totalMembers,
      summary: {
        totalMembers,
        activeMembers
      },
      members: mappedMembers
    };

  } catch (error) {
    console.error('❌ Erro ao buscar dados da comissão:', error);
    throw new Error('Erro ao gerar relatório da comissão');
  }
};

/**
 * Busca dados para relatório de multas
 */
export const getFinesReportData = async (filters: FinesReportFilters): Promise<FinesReportData> => {
  try {
    console.log('🔍 Buscando dados para relatório de multas:', filters);
    console.log('📅 Filtros de data recebidos:', {
      startDate: filters.startDate,
      endDate: filters.endDate
    });

    let query = supabase
      .from('quotas')
      .select(`
        id,
        mes,
        ano,
        multa,
        status,
        data_vencimento,
        data_pagamento,
        created_at,
        updated_at,
        moradores!inner(nome, apartamento)
      `)
      .gt('multa', 0); // Apenas quotas com multa

    // Aplicar filtros de data usando data_vencimento (igual ao relatório financeiro)
    if (filters.startDate) {
      query = query.gte('data_vencimento', filters.startDate);
      console.log('📅 Filtro startDate aplicado:', filters.startDate);
    }
    if (filters.endDate) {
      query = query.lte('data_vencimento', filters.endDate);
      console.log('📅 Filtro endDate aplicado:', filters.endDate);
    }

    // Aplicar filtro de apartamento
    if (filters.apartmentNumber) {
      query = query.eq('moradores.apartamento', filters.apartmentNumber);
    }

    // Aplicar filtro de valor mínimo
    if (filters.minAmount) {
      query = query.gte('multa', filters.minAmount);
    }

    // Aplicar filtro de status
    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'paid') {
        query = query.eq('status', 'Pago');
      } else if (filters.status === 'pending') {
        query = query.eq('status', 'Não Pago');
      }
    }

    const { data: finesData, error } = await query.order('ano', { ascending: false })
                                                  .order('mes', { ascending: false });

    if (error) {
      console.error('❌ Erro na consulta de multas:', error);
      throw error;
    }

    const fines = finesData || [];

    console.log('📊 Dados de multas encontrados:', {
      totalRecords: fines.length,
      sampleData: fines.slice(0, 3).map(f => ({
        apartamento: f.moradores?.apartamento,
        mes: f.mes,
        ano: f.ano,
        multa: f.multa,
        status: f.status
      }))
    });

    // Ordenar por apartamento usando a mesma lógica do ReportFiltersModal (que funciona perfeitamente)
    const sortedFines = [...fines].sort((a, b) => {
      const aptA = a.moradores?.apartamento || 'ZZZ'; // 'ZZZ' para colocar N/A no final
      const aptB = b.moradores?.apartamento || 'ZZZ';

      // Lógica copiada exatamente do ReportFiltersModal (linhas 111-119)
      const numA = parseInt(aptA);
      const numB = parseInt(aptB);
      if (!isNaN(numA) && !isNaN(numB)) {
        return numA - numB;
      }
      return aptA.localeCompare(aptB);
    });

    console.log('📋 Dados ordenados por apartamento:', {
      primeiros10: sortedFines.slice(0, 10).map(f => f.moradores?.apartamento)
    });

    // Calcular resumo usando dados ordenados
    const totalFines = sortedFines.length;
    const paidFines = sortedFines.filter(f => f.status === 'Pago').length;
    const pendingFines = sortedFines.filter(f => f.status === 'Não Pago').length;
    const totalAmount = sortedFines.reduce((sum, f) => sum + (f.multa || 0), 0);
    const paidAmount = sortedFines
      .filter(f => f.status === 'Pago')
      .reduce((sum, f) => sum + (f.multa || 0), 0);
    const pendingAmount = sortedFines
      .filter(f => f.status === 'Não Pago')
      .reduce((sum, f) => sum + (f.multa || 0), 0);

    console.log('📈 Resumo calculado:', {
      totalFines,
      paidFines,
      pendingFines,
      totalAmount,
      paidAmount,
      pendingAmount
    });

    // Mapear dados das multas ordenadas
    const mappedFines = sortedFines.map(f => ({
      id: f.id,
      residentName: f.moradores?.nome || 'N/A',
      apartmentNumber: f.moradores?.apartamento || 'N/A',
      month: f.mes,
      year: f.ano,
      fineAmount: f.multa || 0,
      status: f.status || 'N/A',
      appliedDate: f.created_at || new Date().toISOString(),
      paidDate: f.data_pagamento || undefined,
      reason: `Multa por atraso - ${f.mes}/${f.ano}`
    }));

    return {
      type: 'fines',
      title: 'Relatório de Multas',
      subtitle: 'Condomínio Prédio Azul',
      generatedAt: new Date().toISOString(),
      generatedBy: 'Sistema',
      filters,
      totalRecords: totalFines,
      summary: {
        totalFines,
        paidFines,
        pendingFines,
        totalAmount,
        paidAmount,
        pendingAmount
      },
      fines: mappedFines
    };

  } catch (error) {
    console.error('❌ Erro ao buscar dados de multas:', error);
    throw new Error('Erro ao gerar relatório de multas');
  }
};

/**
 * Função principal para buscar dados de relatório
 */
export const getReportData = async (reportType: ReportType, filters: ReportFilters): Promise<ReportData> => {
  switch (reportType) {
    case 'quotas':
      return getQuotasReportData(filters as QuotasReportFilters);
    case 'residents':
      return getResidentsReportData(filters as ResidentsReportFilters);
    case 'financial':
      return getFinancialReportData(filters as FinancialReportFilters);
    case 'committee':
      return getCommitteeReportData(filters as CommitteeReportFilters);
    case 'fines':
      return getFinesReportData(filters as FinesReportFilters);
    default:
      throw new Error('Tipo de relatório não suportado');
  }
};
