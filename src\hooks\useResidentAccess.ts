import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getResidentsWithAccessStatus,
  createResidentUser,
  revokeResidentAccess,
  revokeAndDeleteResidentCompletely,
  type CreateResidentUserParams,
  type ResidentWithAccess
} from '@/utils/resident-access-helpers';
import { deleteUserCompletely } from '@/utils/user';
import { toast } from 'sonner';

export const useResidentAccess = () => {
  const queryClient = useQueryClient();

  const { data: residentsWithAccess = [], isLoading, error, refetch } = useQuery({
    queryKey: ['residents-with-access'],
    queryFn: getResidentsWithAccessStatus,
    staleTime: 5 * 60 * 1000, // 5 minutos
  });

  const createUserMutation = useMutation({
    mutationFn: async (params: CreateResidentUserParams) => {
      return await createResidentUser(params);
    },
    onSuccess: (data) => {
      if (data && data.success === true && data.user_id) {
        queryClient.invalidateQueries({ queryKey: ['residents-with-access'] });
        queryClient.invalidateQueries({ queryKey: ['moradores'] });
        toast.success(`Acesso criado para ${data.name}!`);
      } else {
        toast.error('Erro: Resposta inválida do servidor');
      }
    },
    onError: (error: Error) => {
      const errorMessage = error.message || 'Erro ao criar acesso';
      toast.error(errorMessage);
    }
  });

  const revokeAccessMutation = useMutation({
    mutationFn: ({ userId, apartmentId }: { userId: string; apartmentId: string }) => 
      revokeResidentAccess(userId, apartmentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['residents-with-access'] });
      toast.success('Acesso revogado com sucesso!');
    },
    onError: (error: Error) => {
      console.error('Error revoking access:', error);
      toast.error(`Erro ao revogar acesso: ${error.message}`);
    }
  });

  const deleteUserCompletelyMutation = useMutation({
    mutationFn: (email: string) => deleteUserCompletely(email),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['residents-with-access'] });
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Utilizador eliminado completamente!');
    },
    onError: (error: Error) => {
      console.error('Error deleting user completely:', error);
      toast.error(`Erro ao eliminar utilizador: ${error.message}`);
    }
  });

  const revokeAndDeleteCompletelyMutation = useMutation({
    mutationFn: (email: string) => revokeAndDeleteResidentCompletely(email),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['residents-with-access'] });
      queryClient.invalidateQueries({ queryKey: ['moradores'] });
      toast.success('Acesso revogado e utilizador eliminado completamente!');
    },
    onError: (error: Error) => {
      console.error('Error revoking and deleting user completely:', error);
      toast.error(`Erro ao revogar acesso e eliminar utilizador: ${error.message}`);
    }
  });

  const createUser = (params: CreateResidentUserParams) => {
    // Reset previous errors
    createUserMutation.reset();

    // Validação básica antes de chamar a mutação
    if (!params.email || !params.password || !params.name || !params.apartment_id) {
      toast.error('Erro: Parâmetros inválidos fornecidos');
      return;
    }

    createUserMutation.mutate(params);
  };



  return {
    residentsWithAccess,
    isLoading,
    error,
    refetch,
    createUser,
    isCreatingUser: createUserMutation.isPending,
    createUserError: createUserMutation.error,
    createUserStatus: createUserMutation.status,
    revokeAccess: revokeAccessMutation.mutate,
    isRevokingAccess: revokeAccessMutation.isPending,
    deleteUserCompletely: deleteUserCompletelyMutation.mutate,
    isDeletingUser: deleteUserCompletelyMutation.isPending,
    revokeAndDeleteCompletely: revokeAndDeleteCompletelyMutation.mutate,
    isRevokingAndDeleting: revokeAndDeleteCompletelyMutation.isPending,
  };
};
