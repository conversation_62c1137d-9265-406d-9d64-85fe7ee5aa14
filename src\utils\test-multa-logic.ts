/**
 * Teste da Lógica de Aplicação de Multas
 * 
 * Este arquivo contém testes para validar se a correção da lógica de multas
 * está funcionando corretamente.
 * 
 * Para executar: Abra o console do navegador e chame testMultaLogic()
 */

import { calculateFineAmount, correctFineOnPayment, MultaConfig } from './quota-calculations';

// Configuração de teste (valores padrão do sistema)
const testConfig: MultaConfig = {
  valorMulta: 1000, // 1000 Kz
  diasAtrasoMulta: 0 // 0 dias de tolerância
};

/**
 * Testa cenários de aplicação de multa
 */
export const testMultaLogic = () => {
  console.log('🧪 INICIANDO TESTES DA LÓGICA DE MULTAS');
  console.log('=====================================');
  
  // Cenário 1: Pagamento ANTES da data limite
  console.log('\n📅 CENÁRIO 1: Pagamento ANTES da data limite');
  const dataVencimento1 = '2025-06-10'; // Dia 10
  const dataPagamento1 = '2025-06-02';  // Dia 2 (antes do limite)
  
  const multa1 = calculateFineAmount(dataPagamento1, dataVencimento1, testConfig);
  const correcao1 = correctFineOnPayment(dataPagamento1, dataVencimento1, testConfig);
  
  console.log(`Data vencimento: ${dataVencimento1}`);
  console.log(`Data pagamento: ${dataPagamento1}`);
  console.log(`Multa calculada: ${multa1} Kz`);
  console.log(`Correção automática: ${correcao1.newFineAmount} Kz`);
  console.log(`✅ ESPERADO: 0 Kz | RESULTADO: ${multa1 === 0 ? 'PASSOU' : 'FALHOU'}`);
  
  // Cenário 2: Pagamento APÓS a data limite
  console.log('\n📅 CENÁRIO 2: Pagamento APÓS a data limite');
  const dataVencimento2 = '2025-06-10'; // Dia 10
  const dataPagamento2 = '2025-06-15';  // Dia 15 (após o limite)
  
  const multa2 = calculateFineAmount(dataPagamento2, dataVencimento2, testConfig);
  const correcao2 = correctFineOnPayment(dataPagamento2, dataVencimento2, testConfig);
  
  console.log(`Data vencimento: ${dataVencimento2}`);
  console.log(`Data pagamento: ${dataPagamento2}`);
  console.log(`Multa calculada: ${multa2} Kz`);
  console.log(`Correção automática: ${correcao2.newFineAmount} Kz`);
  console.log(`✅ ESPERADO: 1000 Kz | RESULTADO: ${multa2 === 1000 ? 'PASSOU' : 'FALHOU'}`);
  
  // Cenário 3: Pagamento EXATAMENTE na data limite
  console.log('\n📅 CENÁRIO 3: Pagamento EXATAMENTE na data limite');
  const dataVencimento3 = '2025-06-10'; // Dia 10
  const dataPagamento3 = '2025-06-10';  // Dia 10 (exatamente no limite)
  
  const multa3 = calculateFineAmount(dataPagamento3, dataVencimento3, testConfig);
  const correcao3 = correctFineOnPayment(dataPagamento3, dataVencimento3, testConfig);
  
  console.log(`Data vencimento: ${dataVencimento3}`);
  console.log(`Data pagamento: ${dataPagamento3}`);
  console.log(`Multa calculada: ${multa3} Kz`);
  console.log(`Correção automática: ${correcao3.newFineAmount} Kz`);
  console.log(`✅ ESPERADO: 0 Kz | RESULTADO: ${multa3 === 0 ? 'PASSOU' : 'FALHOU'}`);
  
  // Cenário 4: Teste com tolerância (3 dias)
  console.log('\n📅 CENÁRIO 4: Teste com tolerância de 3 dias');
  const configComTolerancia: MultaConfig = {
    valorMulta: 1000,
    diasAtrasoMulta: 3 // 3 dias de tolerância
  };
  
  const dataVencimento4 = '2025-06-10'; // Dia 10
  const dataPagamento4 = '2025-06-12';  // Dia 12 (2 dias após, dentro da tolerância)
  
  const multa4 = calculateFineAmount(dataPagamento4, dataVencimento4, configComTolerancia);
  const correcao4 = correctFineOnPayment(dataPagamento4, dataVencimento4, configComTolerancia);
  
  console.log(`Data vencimento: ${dataVencimento4}`);
  console.log(`Data pagamento: ${dataPagamento4}`);
  console.log(`Tolerância: ${configComTolerancia.diasAtrasoMulta} dias`);
  console.log(`Multa calculada: ${multa4} Kz`);
  console.log(`Correção automática: ${correcao4.newFineAmount} Kz`);
  console.log(`✅ ESPERADO: 0 Kz | RESULTADO: ${multa4 === 0 ? 'PASSOU' : 'FALHOU'}`);
  
  // Cenário 5: Teste fora da tolerância
  console.log('\n📅 CENÁRIO 5: Teste fora da tolerância (4 dias após)');
  const dataPagamento5 = '2025-06-14';  // Dia 14 (4 dias após, fora da tolerância de 3)
  
  const multa5 = calculateFineAmount(dataPagamento5, dataVencimento4, configComTolerancia);
  const correcao5 = correctFineOnPayment(dataPagamento5, dataVencimento4, configComTolerancia);
  
  console.log(`Data vencimento: ${dataVencimento4}`);
  console.log(`Data pagamento: ${dataPagamento5}`);
  console.log(`Tolerância: ${configComTolerancia.diasAtrasoMulta} dias`);
  console.log(`Multa calculada: ${multa5} Kz`);
  console.log(`Correção automática: ${correcao5.newFineAmount} Kz`);
  console.log(`✅ ESPERADO: 1000 Kz | RESULTADO: ${multa5 === 1000 ? 'PASSOU' : 'FALHOU'}`);
  
  // Resumo dos testes
  console.log('\n🎯 RESUMO DOS TESTES');
  console.log('===================');
  const testes = [
    { nome: 'Pagamento antes do limite', passou: multa1 === 0 },
    { nome: 'Pagamento após o limite', passou: multa2 === 1000 },
    { nome: 'Pagamento no limite', passou: multa3 === 0 },
    { nome: 'Pagamento dentro da tolerância', passou: multa4 === 0 },
    { nome: 'Pagamento fora da tolerância', passou: multa5 === 1000 }
  ];
  
  const testesPassaram = testes.filter(t => t.passou).length;
  const totalTestes = testes.length;
  
  testes.forEach(teste => {
    console.log(`${teste.passou ? '✅' : '❌'} ${teste.nome}`);
  });
  
  console.log(`\n📊 RESULTADO FINAL: ${testesPassaram}/${totalTestes} testes passaram`);
  
  if (testesPassaram === totalTestes) {
    console.log('🎉 TODOS OS TESTES PASSARAM! A lógica de multas está funcionando corretamente.');
  } else {
    console.log('⚠️ ALGUNS TESTES FALHARAM! Verifique a implementação.');
  }
  
  return { testesPassaram, totalTestes, sucesso: testesPassaram === totalTestes };
};

// Exportar para uso no console do navegador
if (typeof window !== 'undefined') {
  (window as any).testMultaLogic = testMultaLogic;
  console.log('🧪 Teste de multas carregado! Execute testMultaLogic() no console para testar.');
}
