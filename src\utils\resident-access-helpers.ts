import { supabase } from '@/integrations/supabase/client';

// Tipos para o sistema de acesso de moradores
export interface CreateResidentUserParams {
  email: string;
  password: string;
  name: string;
  apartment_id: string;
}

export interface CreateResidentUserResult {
  success: boolean;
  user_id: string;
  email: string;
  name: string;
  apartment_id: string;
  password?: string;
  message?: string;
}

export interface ResidentWithAccess {
  id: string;
  name: string;
  email: string;
  apartment_id: string;
  role: string;
  has_access: boolean;
  user_id?: string;
  access_granted_at?: string;
  access_granted_by?: string;
  first_login: boolean;
}

/**
 * Verifica se um email já existe no sistema
 */
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email.toLowerCase().trim())
      .limit(1);

    if (error) {
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    return false;
  }
};

/**
 * Cria um usuário morador com acesso ao sistema usando Edge Function
 */
export const createResidentUser = async (params: CreateResidentUserParams): Promise<CreateResidentUserResult> => {
  return await createResidentUserViaEdgeFunction(params);
};

/**
 * Método original usando Edge Function
 */
const createResidentUserViaEdgeFunction = async (params: CreateResidentUserParams): Promise<CreateResidentUserResult> => {
  try {
    // Validação preventiva: verificar se email já existe
    const emailExists = await checkEmailExists(params.email);
    if (emailExists) {
      throw new Error('Este email já está registado no sistema');
    }

    // Verificar se o utilizador está autenticado
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (!session || !session.user) {
      throw new Error('Sessão não encontrada. Por favor, faça login novamente.');
    }

    // Verificar se o token está válido
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      throw new Error('Token inválido. Por favor, faça login novamente.');
    }

    // Validação rigorosa dos parâmetros antes de enviar
    if (!params.email || typeof params.email !== 'string' || !params.email.trim()) {
      throw new Error('Email é obrigatório e deve ser uma string válida');
    }
    
    if (!params.password || typeof params.password !== 'string' || params.password.length < 6) {
      throw new Error('Password é obrigatória e deve ter pelo menos 6 caracteres');
    }
    
    if (!params.name || typeof params.name !== 'string' || !params.name.trim()) {
      throw new Error('Nome é obrigatório e deve ser uma string válida');
    }
    
    if (!params.apartment_id || typeof params.apartment_id !== 'string' || !params.apartment_id.trim()) {
      throw new Error('Apartment ID é obrigatório e deve ser uma string válida');
    }

    // Preparar payload com validação rigorosa
    const payload = {
      email: String(params.email).trim().toLowerCase(),
      password: String(params.password),
      name: String(params.name).trim(),
      apartment_id: String(params.apartment_id).trim().toUpperCase()
    };

    // Verificar se o payload pode ser serializado correctamente
    try {
      JSON.stringify(payload);
    } catch (serializationError) {
      throw new Error('Erro na serialização dos dados. Verifique os caracteres especiais.');
    }

    // Debug temporário para verificar o payload
    console.log('🔍 [DEBUG] Payload being sent to Edge Function:', payload);
    console.log('🔍 [DEBUG] Payload JSON:', JSON.stringify(payload));

    // Tentar com fetch direto para ter mais controle
    const supabaseUrl = 'https://qrcegsdhbwgjtkebqjok.supabase.co';
    const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFyY2Vnc2RoYndnanRrZWJxam9rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI5MTIzNDMsImV4cCI6MjA1ODQ4ODM0M30.r94EU08552kYer4TIGBxlg4h-dkqXt6_tOb1wzSm_lc';
    const functionUrl = `${supabaseUrl}/functions/v1/create-resident-user`;

    console.log('🔍 [DEBUG] Function URL:', functionUrl);
    console.log('🔍 [DEBUG] Session token:', session.access_token?.substring(0, 20) + '...');

    const fetchResponse = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        'apikey': supabaseAnonKey
      },
      body: JSON.stringify(payload)
    });

    console.log('🔍 [DEBUG] Fetch response status:', fetchResponse.status);
    console.log('🔍 [DEBUG] Fetch response headers:', Object.fromEntries(fetchResponse.headers.entries()));

    const responseText = await fetchResponse.text();
    console.log('🔍 [DEBUG] Raw response text:', responseText);

    let responseData: any;
    let responseError: any = null;

    if (!fetchResponse.ok) {
      responseError = {
        status: fetchResponse.status,
        statusText: fetchResponse.statusText,
        message: responseText
      };
    } else {
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        responseError = {
          status: 500,
          message: 'Failed to parse response JSON'
        };
      }
    }

    const response = { data: responseData, error: responseError };

    // Verificar se houve erro na chamada da função
    if (response.error) {
      const errorStatus = response.error.status || response.error.code || 500;
      const errorMessage = response.error.message || response.error.error || response.error.statusText || 'Erro desconhecido';

      // Fornecer mensagens de erro mais específicas baseadas no status
      if (errorStatus === 400) {
        throw new Error(`Erro de validação: ${errorMessage}`);
      } else if (errorStatus === 401) {
        throw new Error('Não autorizado. Verifique se tem permissões de administrador.');
      } else if (errorStatus === 403) {
        throw new Error('Acesso negado. Privilégios de administrador necessários.');
      } else if (errorStatus === 404) {
        throw new Error('Função não encontrada. Verifique a configuração do sistema.');
      } else if (errorStatus === 500) {
        throw new Error(`Erro interno do servidor: ${errorMessage}`);
      } else if (errorStatus >= 500) {
        throw new Error(`Erro do servidor (${errorStatus}): ${errorMessage}`);
      } else {
        throw new Error(`Erro na chamada da função (${errorStatus}): ${errorMessage}`);
      }
    }

    // Verificar se data existe
    if (!response.data) {
      throw new Error('Nenhuma resposta recebida da função. Tente novamente.');
    }

    // Verificar se há erro na resposta (Edge Function pode retornar 200 mas com erro no body)
    if (response.data.error) {
      const errorMsg = typeof response.data.error === 'string' ? response.data.error : JSON.stringify(response.data.error);
      throw new Error(`Erro da função: ${errorMsg}`);
    }

    // Verificar se a resposta indica sucesso
    if (response.data.success === false || (!response.data.success && !response.data.user_id)) {
      const errorMsg = response.data.error || response.data.message || 'Operação não foi bem-sucedida';
      throw new Error(errorMsg);
    }

    // Validar estrutura da resposta de sucesso
    if (!response.data.user_id || !response.data.email || !response.data.name || !response.data.apartment_id) {
      throw new Error('Resposta da função está incompleta');
    }

    // Log da resposta completa para debug
    console.log('✅ [createResidentUserViaEdgeFunction] Success response:', response.data);

    return response.data;

  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Erro desconhecido ao criar utilizador');
  }
};

/**
 * Busca todos os moradores com status de acesso
 */
export const getResidentsWithAccessStatus = async (): Promise<ResidentWithAccess[]> => {
  try {
    const { data, error } = await supabase.rpc('get_residents_with_access_status');

    if (error) {
      throw error;
    }

    // Mapear dados da função RPC para o tipo esperado
    return (data || []).map((item: any) => ({
      id: item.morador_id,
      name: item.morador_nome,
      email: item.morador_email,
      apartment_id: item.apartamento,
      role: 'user', // Default role for residents
      has_access: item.has_access,
      user_id: item.user_id,
      access_granted_at: item.access_granted_at,
      first_login: item.first_login
    }));
  } catch (error) {
    return [];
  }
};

export const revokeResidentAccess = async (userId: string, apartmentId: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('revoke_apartment_access', {
      p_user_id: userId,
      p_apartment_id: apartmentId
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    return false;
  }
};

export const generateSecurePassword = (length: number = 12): string => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';

  // Garantir pelo menos um de cada tipo
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
  password += '0123456789'[Math.floor(Math.random() * 10)];
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)];

  // Preencher o resto
  for (let i = password.length; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }

  // Embaralhar
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    return false;
  }
};

export const generateCredentialsPDF = async (credentials: { name: string; email: string; password: string; apartment: string }): Promise<void> => {
  try {
    // Importar jsPDF dinamicamente
    const { default: jsPDF } = await import('jspdf');

    // Criar documento PDF
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;

    // Cores do tema Prédio Azul
    const primaryColor = '#0066B3';
    const textColor = '#374151';
    const lightGray = '#F3F4F6';

    // Função para carregar logo como base64
    const loadImageAsBase64 = (src: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Não foi possível criar contexto do canvas'));
            return;
          }
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          resolve(canvas.toDataURL('image/jpeg', 0.8));
        };
        img.onerror = () => reject(new Error('Erro ao carregar imagem'));
        img.src = src;
      });
    };

    // Adicionar marca d'água
    try {
      const logoBase64 = await loadImageAsBase64('/Predio Azul Logo.jpg');
      if (logoBase64) {
        // Marca d'água (transparente e centralizada)
        doc.saveGraphicsState();
        doc.setGState(new (doc as any).GState({ opacity: 0.1 }));
        doc.addImage(
          logoBase64,
          'JPEG',
          pageWidth / 2 - 40,
          pageHeight / 2 - 30,
          80,
          60
        );
        doc.restoreGraphicsState();

        // Logo no cabeçalho
        doc.addImage(logoBase64, 'JPEG', 15, 15, 30, 20);
      }
    } catch (error) {
      console.warn('Não foi possível carregar o logo:', error);
    }

    // Título principal
    doc.setFontSize(18);
    doc.setTextColor(primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('CREDENCIAIS DE ACESSO', pageWidth / 2, 25, { align: 'center' });

    // Subtítulo
    doc.setFontSize(14);
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'normal');
    doc.text('Condomínio Prédio Azul', pageWidth / 2, 32, { align: 'center' });

    // Data de geração
    doc.setFontSize(10);
    doc.setTextColor('#6B7280');
    doc.text(
      `Gerado em: ${new Date().toLocaleDateString('pt-PT')}`,
      pageWidth - 15,
      15,
      { align: 'right' }
    );

    // Linha separadora
    doc.setDrawColor(primaryColor);
    doc.setLineWidth(0.5);
    doc.line(15, 40, pageWidth - 15, 40);

    // Dados das credenciais
    let yPosition = 60;

    // Caixa de destaque para as credenciais
    doc.setFillColor(lightGray);
    doc.rect(15, yPosition - 5, pageWidth - 30, 60, 'F');

    // Borda da caixa
    doc.setDrawColor(primaryColor);
    doc.setLineWidth(0.3);
    doc.rect(15, yPosition - 5, pageWidth - 30, 60);

    // Título da seção
    doc.setFontSize(14);
    doc.setTextColor(primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('DADOS DE ACESSO:', 20, yPosition + 5);

    yPosition += 15;

    // Dados das credenciais
    doc.setFontSize(12);
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'normal');

    const credentialsData = [
      ['Nome:', credentials.name],
      ['Apartamento:', credentials.apartment],
      ['Email:', credentials.email],
      ['Senha:', credentials.password]
    ];

    credentialsData.forEach(([label, value]) => {
      doc.setFont('helvetica', 'bold');
      doc.text(label, 25, yPosition);
      doc.setFont('helvetica', 'normal');
      doc.text(value, 60, yPosition);
      yPosition += 8;
    });

    // Instruções importantes
    yPosition += 15;
    doc.setFontSize(14);
    doc.setTextColor(primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.text('INSTRUÇÕES IMPORTANTES:', 15, yPosition);

    yPosition += 10;
    doc.setFontSize(11);
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'normal');

    const instructions = [
      '• Esta é sua senha temporária',
      '• Você será solicitado a alterá-la no primeiro acesso',
      '• Mantenha suas credenciais em segurança',
      '• Em caso de dúvidas, contacte a administração'
    ];

    instructions.forEach(instruction => {
      doc.text(instruction, 20, yPosition);
      yPosition += 7;
    });

    // Rodapé
    const footerY = pageHeight - 25;
    doc.setDrawColor(lightGray);
    doc.setLineWidth(0.3);
    doc.line(15, footerY, pageWidth - 15, footerY);

    doc.setFontSize(8);
    doc.setTextColor('#6B7280');
    doc.setFont('helvetica', 'normal');
    doc.text('Desenvolvido por CC', 15, footerY + 10);
    doc.text('Condomínio Prédio Azul', pageWidth - 15, footerY + 10, { align: 'right' });

    // Gerar nome do arquivo
    const timestamp = new Date().toISOString().slice(0, 10);
    const filename = `credenciais_${credentials.apartment}_${credentials.name.replace(/\s+/g, '_')}_${timestamp}.pdf`;

    // Salvar PDF
    doc.save(filename);

  } catch (error) {
    console.error('Erro ao gerar PDF de credenciais:', error);
    throw new Error('Erro ao gerar PDF de credenciais');
  }
};

/**
 * Envia credenciais por email para o morador
 */
export const sendCredentialsEmail = async (
  credentials: { name: string; email: string; password: string; apartment: string },
  sendToResident: boolean
): Promise<boolean | 'simulated'> => {
  try {
    if (!sendToResident) {
      console.log('📧 [sendCredentialsEmail] No email sending requested');
      return true;
    }

    console.log('📧 [sendCredentialsEmail] Sending credentials email...');

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('Sessão não encontrada');
    }

    const payload = {
      to: credentials.email,
      name: credentials.name,
      apartment: credentials.apartment,
      email: credentials.email,
      password: credentials.password
    };

    const response = await fetch('https://qrcegsdhbwgjtkebqjok.supabase.co/functions/v1/send-credentials-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFyY2Vnc2RoYndnanRrZWJxam9rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI5MTIzNDMsImV4cCI6MjA1ODQ4ODM0M30.r94EU08552kYer4TIGBxlg4h-dkqXt6_tOb1wzSm_lc'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ [sendCredentialsEmail] Email API error:', errorData);
      throw new Error(errorData.error || errorData.message || 'Erro ao enviar email');
    }

    const result = await response.json();
    console.log('✅ [sendCredentialsEmail] Email sent successfully:', result);

    // Check if email was actually sent or just simulated
    if (result.details?.simulated) {
      console.warn('⚠️ [sendCredentialsEmail] Email was simulated (RESEND_API_KEY not configured)');
      return 'simulated';
    }

    return result.success;
  } catch (error) {
    console.error('❌ [sendCredentialsEmail] Error sending email:', error);
    // Don't throw error - email sending is not critical for user creation
    return false;
  }
};
